<?php defined('BASEPATH') OR exit('No direct script access allowed');

class User_provisioning_controller extends CI_Controller {

	public function __construct()	{
        parent::__construct();

		if (!$this->ion_auth->logged_in()) {
            redirect('auth/login', 'refresh');
        }

        $this->load->model('user_provisioning_model');
        $this->load->model('student/Student_Model');
        $this->config->load('form_elements');
        $this->load->library('bitly');
    }

    public function index(){
        if (!$this->authorization->isAuthorized('USER_MANAGEMENT.PROVISION_PARENTS')) {
            redirect('dashboard', 'refresh');
        }	
        // $data['classSectionList'] = $this->Student_Model->getClassSectionNames();
        $yearId = $this->acad_year->getAcadYearId();
        $data['classSectionList'] = $this->user_provisioning_model->getClassSectionNames($yearId);
        $data['classSectionId'] = 0;
        // echo '<pre>'; print_r(json_encode($data['classSectionList'])); die();
        $data['sendTo'] = 'Both';
        $data['status'] = 'All';
        $data['student_name'] = '';
        $data['main_content'] = 'user_provisioning/index';
        $this->load->view('inc/template', $data);
    }

    public function get_studentData($class=0, $sendTo='Both',$status='All'){
        if (!$this->authorization->isAuthorized('USER_MANAGEMENT.PROVISION_PARENTS')) {
            redirect('dashboard', 'refresh');
        }	
        $data['classSectionId'] = $class;

        $data['allow_activated'] = $this->settings->getSetting('allow_provisioning_for_activated');
        
        $data['sendTo'] = $sendTo;
        $data['status'] = urldecode($status);
        $data['student_name'] = '';
        if(isset($_POST['classSectionId']))
            $data['classSectionId'] = $_POST['classSectionId'];
        if(isset($_POST['sendTo']))
            $data['sendTo'] = $_POST['sendTo'];
        if(isset($_POST['status']))
            $data['status'] = $_POST['status'];
        if(isset($_POST['student_name']))
            $data['student_name'] = $_POST['student_name'];
        //echo $data['classSectionId']; die();
        // echo $data['status']; die();
        
        $data['getstudents'] = $this->user_provisioning_model->getstudentDetails($data['classSectionId'],$data['sendTo'],$data['status'], $data['student_name']);
        foreach ($data['getstudents'] as $key => $student) {
            $student->status = 'Not Provisioned';
            if($student->Active == 1){
                $student->status = 'Activated';
            } else if($student->attempts >= 6) {
                $student->status = 'Failed Activation';
            } else if($student->attempts <= 5 && $student->attempts >= 1) {
                $student->status = 'Provisioned';
            }
            if($student->Active == 1 && $student->loggedin_atleast_once) {
                $student->status = 'LoggedIn';
            }
        }
        // echo "<pre>"; print_r($data['getstudents']); die();
        $data['classSectionList'] = $this->Student_Model->getClassSectionNames();
        // echo '<pre>'; print_r($data['status']); 
        // echo '<pre>'; print_r($data['classSectionList']); die();
        $data['main_content'] = 'user_provisioning/index';
        $this->load->view('inc/template', $data);
    }

    public function student_provisionbyid($studentId, $sendTo='Both',$status='All'){
        $data['student_uid'] = $studentId;
        if (!$this->authorization->isAuthorized('USER_MANAGEMENT.PROVISION_PARENTS')) {
            redirect('dashboard', 'refresh');
        }
        $data['stdData'] = $this->user_provisioning_model->get_student_details_by_id($studentId);
        $data['classSectionId'] = $data['stdData']->csId.'_'. $data['stdData']->cId;
        $data['allow_activated'] = $this->settings->getSetting('allow_provisioning_for_activated');
        $data['sendTo'] = $sendTo;
        $data['status'] = urldecode($status);
        if(isset($_POST['sendTo']))
            $data['sendTo'] = $_POST['sendTo'];
        if(isset($_POST['status']))
            $data['status'] = $_POST['status'];

        $data['getstudents'] = $this->user_provisioning_model->getstudentDetailsbyId($data['stdData']->stdId,$data['sendTo'],$data['status']);
        foreach ($data['getstudents'] as $key => $student) {
            $student->status = 'Not Provisioned';
            if($student->Active == 1){
                $student->status = 'Activated';
            } else if($student->attempts >= 6) {
                $student->status = 'Failed Activation';
            } else if($student->attempts <= 5 && $student->attempts >= 1) {
                $student->status = 'Provisioned';
            }
            if($student->Active == 1 && $student->loggedin_atleast_once) {
                $student->status = 'LoggedIn';
            }
        }
        // echo "<pre>"; print_r($data['getstudents']); die();
        if ($this->mobile_detect->isTablet()) {
          $data['main_content'] = 'user_provisioning/provision_student_tablet';
        }else if($this->mobile_detect->isMobile()){
          $data['main_content'] = 'user_provisioning/provision_student_mobile';
        }else{
          $data['main_content'] = 'user_provisioning/provision_student';      	
        }
        $this->load->view('inc/template', $data);
    }
    private function _addProvision($codes, $type) {
        $pIds = [];
        $updateData = [];
        if($type == 'send') {
            if (is_array($codes) && !empty($codes)) {
            foreach ($codes as $pid => $code) {
                $pIds[] = $pid;
                $updateData[] = array(
                    'parent_id' => $pid,
                    'code' => $code,
                    'attempts' => 1,
                    'communication_type' => 'sms'
                );
            }
            $this->user_provisioning_model->deleteAttemptsById($pIds);
            $this->user_provisioning_model->addNewAttempts($updateData, 'insert');
            }
        } else {
            if (is_array($codes) && !empty($codes)) {
            foreach ($codes as $pid => $code) {
                $pIds[] = $pid;
                $updateData[] = array(
                    'parent_id' => $pid,
                    'code' => $code,
                    'attempts' => 1,
                    'communication_type' => 'sms'
                );
            }
            $this->user_provisioning_model->addNewAttempts($updateData, 'update');
            }
        }
    }

    private function _send_unique_texts($input) {
        $this->load->helper('texting_helper');
        return sendUniqueText($input);
    }

    private function new_sms_sender($number, $message) {
        $message = urlencode($message);
        $api_key = 'adwm0e5HYvQE3TNI';
        $senerid = 'NXTSMS';
        //http://promotional.mysmsbasket.com/V2/http-api.php?apikey=XXXXXXXXXXXXXXXX&senderid=XXXXXX&number=XXXXXXXXXXX,XXXXXXXXXXX,XXXXXXXXXXX&message=hello there&format=json
        $url = 'http://promotional.mysmsbasket.com/V2/http-api.php';

        // $get_url = "$url?apikey=$api_key&senderid=$senerid&number=$number&message=$message&format=json&";

        $curl = curl_init();
        curl_setopt_array($curl, array(
          CURLOPT_URL => $url,
          CURLOPT_RETURNTRANSFER => true,
          CURLOPT_ENCODING => "",
          CURLOPT_MAXREDIRS => 10,
          CURLOPT_TIMEOUT => 30,
          CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
          CURLOPT_CUSTOMREQUEST => "POST",
          CURLOPT_POSTFIELDS => "apikey=".$api_key."&senderid=".$senerid."&number=".$number."&message=".$message."&format=json",
          CURLOPT_HTTPHEADER => array(
            "Accept: application/json",
            "Cache-Control: no-cache",
            "Content-Type: application/x-www-form-urlencoded"
          ),
        ));

        $response = curl_exec($curl);
        $err = curl_error($curl);
        curl_close($curl);

        $result = json_decode($response);
        if($result->status == 'OK') {
            return 1;
        }
        return 0;
    }

    private function _communicateByNewSMS($parentMessages) {
        $parentIds = array();
        $userIds = array();
        foreach ($parentMessages as $pid => $pSMS) {
            $parentIds[] = $pid;
        }
        $parents = $this->user_provisioning_model->getEmailsByParentId($parentIds);

        foreach ($parents as $key => $parent) {
            if($parent->mobile_no) {
                $this->new_sms_sender($parent->mobile_no, $parentMessages[$parent->parentId]);
            }
        }
    }

    private function _communicateBySMS($stdMessages, $process) {
        $sh_type = 'Student';
        $source = 'Parent User Provisioning';
        if($process == 'activation') {
            $source = 'Parent User Activation';
        }
        
        $sent_by = $this->authorization->getAvatarId();
        $sent_to_str = 'Student Individual';
        if(!empty($stdMessages['Father'])) {
            // $messages = array_values($stdMessages['Father']);
            // $message = $messages[0];
            $input_arr = array();
            $input_arr['student_id_messages'] = $stdMessages['Father'];
            $input_arr['mode'] = 'sms';
            $input_arr['source'] = 'User Provisioning';
            $input_arr['send_to'] = 'Father';
            $res = $this->_send_unique_texts($input_arr);
            if($res['error'] != '') {
                return 0;
            }
            /*$status1 = sendCustomMsg($stdMessages['Father'], $sh_type, $source, $message, $sent_by, $sent_to_str, 'Father');
            if($status1 == -1)
                return -1;*/
        }

        if(!empty($stdMessages['Mother'])) {
            // $messages = array_values($stdMessages['Mother']);
            // $message = $messages[0];
            $input_arr = array();
            $input_arr['student_id_messages'] = $stdMessages['Mother'];
            $input_arr['mode'] = 'sms';
            $input_arr['source'] = 'User Provisioning';
            $input_arr['send_to'] = 'Mother';
            $res = $this->_send_unique_texts($input_arr);
            if($res['error'] != '') {
                return 0;
            }
            /*$status2 = sendCustomMsg($stdMessages['Mother'], $sh_type, $source, $message, $sent_by, $sent_to_str, 'Mother');
            if($status2 == -1)
                return -1;*/
        }
        return 1;

        /*if($status1 || $status2) {
            return 1;
        } else {
            return 0;
        }*/
    }

    private function _communicateByEmail($parentEmails, $process) {
        $parentIds = array();
        $userIds = array();
        foreach ($parentEmails as $pid => $pEmail) {
            $parentIds[] = $pid;
        }

        $emails = $this->user_provisioning_model->getEmailsByParentId($parentIds);

        $mailIds = array();
        $messages = array();
        $usernames = array();
        foreach ($emails as $key => $email) {
            array_push($mailIds, $email->email);
            array_push($usernames, $email->username);
            array_push($messages, $parentEmails[$email->parentId]);
        }
        $set = $this->user_provisioning_model->get_user_provision_email_template();  
        // $subject = 'Account activation link';
        // if($process == 'activation') {
        //     $subject = 'App link with credentials';
        // }
        // $template = 'demo';
        $data = array(
            'send_unique_emails' => 1,
            'message' => $messages,
            'subject' => isset($set->email_subject) ? $set->email_subject : '',
            'mail_username' => $usernames,
            'email_ids' => $mailIds,
            // 'template' => $template
        );


    //New Function. Sends email by taking an Email Template.
        return $this->_emailSender_provision($data);

    //Old Function. Sends email by taking SMS content.
        // return _emailSender($data);
    }

    public function _emailSender_provision($data){
        $set = $this->user_provisioning_model->get_user_provision_email_template();  
        $from_name = $this->settings->getSetting('school_name');
        if(! isset($set->registered_email) && empty($set->registered_email)){
            return 0;
        }   
        $from_email = $set->registered_email;
        // $from_name = $set->email_subject;
        $smtp_user = CONFIG_ENV['smtp_user'];
        $smtp_pass = urlencode(CONFIG_ENV['smtp_pass']);
        $smtp_host = CONFIG_ENV['smtp_host'];
        $smtp_port = CONFIG_ENV['smtp_port'];

        $data['from_email'] = $from_email;
        $data['from_name'] = $from_name;
        $data['smtp_user'] = $smtp_user;
        $data['smtp_pass'] = $smtp_pass;
        $data['smtp_host'] = $smtp_host;
        $data['smtp_port'] = $smtp_port;

        // echo "<pre>"; print_r($data); die();
        $data = http_build_query($data);

       

        $curl = curl_init();

        $username = CONFIG_ENV['job_server_username'];
        $password = CONFIG_ENV['job_server_password'];
        curl_setopt_array($curl, array(
            CURLOPT_URL => CONFIG_ENV['job_server_unique_email_uri'],
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_USERPWD => $username . ":" . $password,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "POST",
            CURLOPT_POST => 1,
            CURLOPT_POSTFIELDS => $data,
            CURLOPT_HTTPHEADER => array(
                "Accept: application/json",
                "Cache-Control: no-cache",
                "Content-Type: application/x-www-form-urlencoded",
                "Postman-Token: 090abdb9-b680-4492-b8b7-db81867b114e"
            ),
        ));

        $response = curl_exec($curl);

        //echo "<pre>";  print_r($response); die();
        $err = curl_error($curl);

        curl_close($curl);

        if ($err) {
          return 0;
        } else {
          return 1;
        }
    }

    private function _emailSender($data) {
       
       
        $set = $this->settings->getSetting('email_settings');
        $from_email = $set->from_email;
        $from_name = $set->from_name;
        // $smtp_user = getenv('smtp_user');
        // $smtp_pass = urlencode(getenv('smtp_pass'));
        // $smtp_host = getenv('smtp_host');
        // $smtp_port = getenv('smtp_port');


        $smtp_user = CONFIG_ENV['smtp_user'];
        $smtp_pass = urlencode(CONFIG_ENV['smtp_pass']);
        $smtp_host = CONFIG_ENV['smtp_host'];
        $smtp_port = CONFIG_ENV['smtp_port'];

        $data['from_email'] = $from_email;
        $data['from_name'] = $from_name;
        $data['smtp_user'] = $smtp_user;
        $data['smtp_pass'] = $smtp_pass;
        $data['smtp_host'] = $smtp_host;
        $data['smtp_port'] = $smtp_port;
        
        // echo '<pre>';print_r($data);die();

        $data = http_build_query($data);

        $curl = curl_init();

	    $username = CONFIG_ENV['job_server_username'];
	    $password = CONFIG_ENV['job_server_password'];
        curl_setopt_array($curl, array(
            CURLOPT_URL => CONFIG_ENV['job_server_unique_email_uri'],
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_USERPWD => $username . ":" . $password,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "POST",
            CURLOPT_POST => 1,
            CURLOPT_POSTFIELDS => $data,
            CURLOPT_HTTPHEADER => array(
                "Accept: application/json",
                "Cache-Control: no-cache",
                "Content-Type: application/x-www-form-urlencoded",
                "Postman-Token: 090abdb9-b680-4492-b8b7-db81867b114e"
            ),
        ));

        $response = curl_exec($curl);

        //echo "<pre>";  print_r($response); die();
        $err = curl_error($curl);

        curl_close($curl);

        if ($err) {
          return 0;
        } else {
          return 1;
        }
    }
    
    public function sendSms(){
        if (!$this->authorization->isAuthorized('USER_MANAGEMENT.PROVISION_PARENTS')) {
            redirect('dashboard', 'refresh');
        }
        $students = $_POST['students'];
        $messages = $_POST['messages'];
        $codes = $_POST['codes'];
        $process = $_POST['process'];
        $sendTo = $_POST['sendTo'];
        $class = $_POST['class'];
        $communication_type = $_POST['cm_type'];
        $state = $_POST['state'];
        $smsType = $_POST['smsType']; // sendSms or resendSms

        //Adding codes into user_prov_login_att table
        if($process == 'provision') {
            $this->_addProvision($codes, $smsType);
        }
        else if($process == 're-provision') {
            $this->user_provisioning_model->refreshAttempts($codes);
        }

        $parentEmails = [];
        $idMessages['Father'] = array();
        $idMessages['Mother'] = array();
        $userIds = array();
        foreach ($messages as $ids => $msg) {
            list($stdId, $pId, $relation, $user_id) = explode("_",$ids);
            $parentEmails[$pId] = $msg;
            $idMessages[$relation][$stdId] = $msg;
            array_push($userIds, $user_id);
        }
        if($process == 'activation') {
            $response = $this->user_provisioning_model->activateUsers($userIds);
            if($response == 0) {
                $this->session->set_flashdata('flashError', 'Unable to activate users');
                redirect('user_provisioning_controller/get_studentData/'.$class.'/'.$sendTo.'/'.$state);
            }
        }

        if($communication_type == 'email' || $communication_type == 'both') {
            $status = $this->_communicateByEmail($parentEmails, $process);
            if($status == 1) {
                $this->session->set_flashdata('flashSuccess', 'Successfully Email Sent');
            } else {
                $this->session->set_flashdata('flashError', 'Something Went Wrong..');
            }
        } 
        if($communication_type == 'sms' || $communication_type == 'both'){
            $school = $this->settings->getSetting('school_short_name');
            // if($school == 'englishroots') {
            //     $this->_communicateByNewSMS($parentEmails);
            //     $status = 1;
            // } else {
            $status = $this->_communicateBySMS($idMessages, $process);
            // }
            if($status == 1) {
                $this->session->set_flashdata('flashSuccess', 'Successfully SMS Sent');
            } else if($status == 0){
                $this->session->set_flashdata('flashError', 'Something Went Wrong..');
            } else if($status == -1) {
                $this->session->set_flashdata('flashError', 'Not enough credits available');
            }
        }
        redirect('user_provisioning_controller/get_studentData/'.$class.'/'.$sendTo.'/'.$state);
    }

    private function generatePassword($length = 10) {
        return substr(str_shuffle(str_repeat($x='abcdefghijkmnopqrstuvwxyz', ceil($length/strlen($x)) )),1,$length);
    }

    private function generateRandomCode($length = 6) {
        return substr(str_shuffle(str_repeat($x='ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz1234567890', ceil($length/strlen($x)) )),1,$length);
    }

    public function verifyMobile(){
        $this->acad_year->loadAcadYearDataToSession();
        $data['code'] = $this->uri->segment(3);
        $id = $this->user_provisioning_model->getParentIdByCode($data['code']);
        if($id == 0) {
            redirect('user_provisioning_controller/wrongUrl');
        }
        $data['stdData'] = $this->user_provisioning_model->getstdId($id);
        $data['userData'] = $this->user_provisioning_model->getUserData($id);
        if($data['userData']->active == 1) {
            $this->load->view('user_provisioning/activated_user', $data);
        }
        $attempts = $this->user_provisioning_model->getAttempts($id, $data['code']);
        if($attempts>=6){
           $this->load->view('user_provisioning/attempts');  
        }else{
            $this->load->view('user_provisioning/instructions/index', $data);  
        }
    }

    public function activationPage(){
        $this->acad_year->loadAcadYearDataToSession();
        $data['code'] = $this->uri->segment(3);
        $id = $this->user_provisioning_model->getParentIdByCode($data['code']);
        if($id == 0) {
            redirect('user_provisioning_controller/wrongUrl');
            return;
        }
        $data['stdData'] = $this->user_provisioning_model->getstdId($id);
        $data['userData'] = $this->user_provisioning_model->getUserData($id);
        if($data['userData']->active == 1) {
            $this->load->view('user_provisioning/activated_user', $data);
            return;
        }
        $attempts = $this->user_provisioning_model->getAttempts($id, $data['code']);
        if($attempts>=6){
           $this->load->view('user_provisioning/attempts');  
        }else{
            $data['classes'] = $this->user_provisioning_model->getClasses();
            $data['stdData'] = $this->user_provisioning_model->getstdId($id);
            $data['attempts'] = $attempts;
            $data['error'] = 0;
            $this->load->view('user_provisioning/verify_mobile', $data);
        }
    }

    public function wrongUrl(){
        $this->load->view('user_provisioning/wrongUrl');
    }

    public function updateUserStatus(){
        $this->acad_year->loadAcadYearDataToSession();
        $code = $this->uri->segment(3);
        $id = $this->user_provisioning_model->getParentIdByCode($code);
        if($id == 0) {
            redirect('user_provisioning_controller/wrongUrl');
        }
        $com_type = $this->user_provisioning_model->getCommunicationType($code);
        $fields = $this->settings->getSetting('user_provisioning_challenge_fields')->fields;
        $data['stdData'] = $this->user_provisioning_model->getstdId($id);
        $data['userData'] = $this->user_provisioning_model->getUserData($id);
        if($data['userData']->active == 1) {
            $data['status'] = 1;
            $data['message'] = "";
            $this->load->view('user_provisioning/activated_user');
            return;
        }
        // echo "<pre>"; print_r($data['userData']);die();
        foreach($fields as $key => $field){
            $chField = $field->name;
            $a = $chField.'_db';
            $data["$a"] = $data['stdData']->$chField; 
        }
       
        $x = '(1)';
        foreach ($fields as $key => $field) {
            $chField = $field->name;
            $a = $chField.'_db';
            $b = $chField.'_form';
            $x = $x."&&('".$data["$a"]."'=='".$_POST["$b"]."')";
        }

        $result = eval('return'.$x.';');
        $flag = 0;
        if($result){
            $userId = $data['userData']->userId;
            $username = $data['userData']->username;
            $mobile_no = $data['userData']->mobile_no;
            $relation = $data['userData']->relation_type;
            $stdId = $data['stdData']->stdId;
            $flag = 1;
            $attempts = $this->user_provisioning_model->addAttempt($id, $code);
            // $activate = $this->user_provisioning_model->updateStatus($id);
            $password = $this->generatePassword(6);//generate random password and update it to the user
            $pdata = array(
                'password' => $password,
                );
            $result = $this->ion_auth->update($userId, $pdata);
            if($result) {
                $part1 = $this->settings->getSetting('user_provisioning_manual_cridential_part_1');
                $part2 = $this->settings->getSetting('user_provisioning_manual_cridential_part_2');
                $message = $this->settings->getSetting('user_provisioning_cridential_message');
                $cridential = "\nUsername: $username \nPassword: $password";
                $sms_content = "$message, $part1\nCredentials for ".$data['userData']->parentName." -$cridential\n".$part2."";
                $support = $this->settings->getSetting('user_support_email');
                if($com_type == 'sms') {
                    $insId = sendCommonSMS(array($stdId), 'Student', 'Parent User Provisioning', $sms_content, 0,'Student Individual', $relation);
                
                    // $insId = sendToCustomNumbers(array($mobile_no),'USER_PROVISIONING', $sms_content);
                    if($insId > 0) {
                        $data['status'] = 1;
                        $data['message'] = "An SMS has been sent to your number ".$mobile_no. " with the login credentials.";
                        $data['school_code'] = $this->settings->getSetting('user_provisioning_school_code');
                        $data['username'] = $username;
                        $data['password'] = $password;
                        $data['note'] = '(Credentials are sent through SMS too)';
                        $data['type'] = 'sms';
                        $activate = $this->user_provisioning_model->updateStatus($id);
                    }else {
                        $data['status'] = 0;
                        $data['message'] = "Unable to send sms to ". $mobile_no. ". Email <a href='".$support."'>Tech support</a> for support.";
                    }
                } else if($com_type = 'email') {
                    $subject = 'Login Credentials';
                    $template = 'demo';
                    $emailData = $this->user_provisioning_model->getParentData($id);
                    $data = array(
                        'send_unique_emails' => 1,
                        'message' => [$sms_content],
                        'subject' => $subject,
                        'mail_username' => [$emailData->name],
                        'email_ids' => [$emailData->email],
                        'template' => $template
                    );

                    $status = $this->_emailSender($data);
                    if($status > 0) {
                        $data['status'] = 1;
                        $data['message'] = "An Email has been sent to your email id ".$emailData->email. " with the login credentials.";
                        $data['school_code'] = $this->settings->getSetting('user_provisioning_school_code');
                        $data['username'] = $username;
                        $data['password'] = $password;
                        $data['note'] = '(Credentials are sent through Email too)';
                        $data['type'] = 'email';
                        $activate = $this->user_provisioning_model->updateStatus($id);
                    }else {
                        $data['status'] = 0;
                        $data['message'] = "Unable to send email to ". $emailData->email. ". Email <a href='".$support."'>Tech support</a> for support.";
                    }
                }
                
                $school = $this->settings->getSetting('user_provisioning_school_code');
                $this->load->view('user_provisioning/instructions/'.$school, $data);
            } else {
                $flag = 0;
            }
        }
        if($flag == 0) {
            $data['error'] = 1;
            $data['code'] = $code;
            $attempts = $this->user_provisioning_model->addAttempt($id, $code);
            $data['attempts'] = $attempts->attempts;
            if($data['attempts']>=6){
               $this->load->view('user_provisioning/attempts'); 
            }else{
                $this->session->set_flashdata('flashError', 'Something Went Wrong..');
                redirect('user_provisioning_controller/verifyMobile/'.$code,$data);   
            }
        }
    }

    public function getPreview(){
        if (!$this->authorization->isAuthorized('USER_MANAGEMENT.PROVISION_PARENTS')) {
            redirect('dashboard', 'refresh');
        }	
        $students = $this->user_provisioning_model->getPreviewData();
        $conf_msg = $this->settings->getSetting('user_provisioning_link_message_body');
        $school = $this->settings->getSetting('school_short_name');
        $msg_footer = $this->settings->getSetting('user_provisioning_link_message_footer');
        $part1 = $this->settings->getSetting('user_provisioning_manual_cridential_part_1');
        $part2 = $this->settings->getSetting('user_provisioning_manual_cridential_part_2');
        $existingCodes = array();
        if($_POST['process'] == 'provision') {
            $existingCodes = $this->user_provisioning_model->getExistingCodes();
        }

        $message_for_credit_calculation = '';
        if ($_POST['ctype'] == 'email') {
            $email_content = $this->user_provisioning_model->get_user_provision_email_template();
        }
        $pData = array();
        foreach($students as $student){
            if($_POST['ctype'] == 'sms')
                $message_by = $student->mobile;
            else if($_POST['ctype'] == 'email')
                $message_by = $student->email;
            else if($_POST['ctype'] == 'both')
                $message_by = "$student->email ($student->mobile)";

            //checking if parent dont have mobile or email or both 
            if($message_by == '' || preg_match('/^\s\(\)$/',$message_by)) {
                continue;
            }
            

            //$name = "$student->parentName ($student->relation_type of $student->studentName)";
            // $name = "$student->relation_type of $student->studentName";
            $name = "$student->studentName";

            if($_POST['process'] == 'provision') {
                //generating random code for url
                do {
                    $encId = $this->generateRandomCode(6);
                }while(in_array($encId, $existingCodes));
                array_push($existingCodes, $encId);
                $url = site_url('user_provisioning_controller/verifyMobile/').$encId;
                $message = "Dear $name,\n$conf_msg Please click on the following link - $url , to activate your account. This is a personalised link. DO NOT share this link with anybody.\n$msg_footer.";

            } else if($_POST['process'] == 're-provision') {
                $encId = $this->user_provisioning_model->getCode($student->pid);
                $url = site_url('user_provisioning_controller/verifyMobile/').$encId;
                $message = "Dear $name,\n$conf_msg Please click on the following link - $url , to activate your account. This is a personalised link. DO NOT share this link with anybody.\n$msg_footer.";
            } else {
                $encId = 0;
                $randomString = $this->generatePassword(6);
                /*if($student->username != $student->mobile) {
                    $user_data = array(
                        'username' => $student->mobile
                    );
                    $status = $this->ion_auth->update($student->user_id, $user_data);
                    if($status) {
                        $student->username = $student->mobile;
                    }
                }*/
                $res = $this->user_provisioning_model->resetPassword($student->user_id,$randomString);
                // if($school == 'demoschool') {
                    $message = str_replace("%%username%%", $student->username, $part1);
                    $message = str_replace("%%password%%", $randomString, $message);
                // } else {
                //     $message = "Dear $name,\n$part1\nUsername: $student->username\nPassword: $randomString\n$part2";
                // }
                if ($_POST['ctype'] == 'email') {

                    $message = str_replace("%%student_name%%", $name, $email_content->content);
                    $message = str_replace("%%username%%", $student->username, $message);
                    $message = str_replace("%%password%%", $randomString, $message);
                }
            }
            if(strlen($message_for_credit_calculation) < strlen($message)) {
                $message_for_credit_calculation = $message;
                //get the largest message for credits calculation
            }
            
            $pData[] = array(
                'std_id' => $student->std_id,
                'pid' => $student->pid,
                'user_id' => $student->user_id,
                'relation_type' => $student->relation_type,
                'name' => $name,
                'message' => $message,
                'message_by' => $message_by,
                'code' => $encId
            );
        }

        $is_credits_available = 1;
        if($_POST['ctype'] == 'sms' || $_POST['ctype'] == 'both') {
            $this->load->helper('texting_helper');
            $is_credits_available = checkCredits($message_for_credit_calculation, count($pData), 'parent');
        }
        echo json_encode(array('preview' => $pData, 'credits_available' => $is_credits_available));
    }

    public function getActivationPreview(){
        if (!$this->authorization->isAuthorized('USER_MANAGEMENT.PROVISION_PARENTS')) {
            redirect('dashboard', 'refresh');
        }	
        $userIds = $_POST['userIds'];
        $stdData = $this->user_provisioning_model->getActivationPreviewData($userIds);
        $htmlStr = '<thead><tr><th>#</th><th>Message</th></tr></thead><tbody>';
        $i = 1;
        $part1 = $this->settings->getSetting('user_provisioning_manual_cridential_part_1');
        $part2 = $this->settings->getSetting('user_provisioning_manual_cridential_part_2');
        $school_code = $this->settings->getSetting('user_provisioning_school_code');

        foreach($stdData as $key => $student){
            $randomString = $this->generatePassword(6);
            $message = "Dear ".$student->first_name . "," . $part1. "Ward Name: " . $student->stuName . "\nSchool code: ".$school_code."\nUser name: " . $student->username. "\nPassword: " . $randomString . $part2;
            $htmlStr .= '<tr><td>'.$i++.'</td>';
            $htmlStr .= '<td>'.$message.'</td></tr>';
        }
        $htmlStr .= '</tbody></table>';
        echo json_encode($htmlStr);
    }

    public function activateSMS(){
        if (!$this->authorization->isAuthorized('USER_MANAGEMENT.PROVISION_PARENTS')) {
            redirect('dashboard', 'refresh');
        }	
        $students = $_POST['students'];
        $sendTo = $_POST['sendTo'];
        $class = $_POST['class'];
        $state = $_POST['state'];
        $smsType = $_POST['smsType'];
        $communication_type = $_POST['cm_type'];

        if($communication_type == 'email' || $communication_type == 'both') {
            $status = $this->_communicateByEmail($students, $smsType, 'activation');
            if($status == 1) {
                $this->session->set_flashdata('flashSuccess', 'Successfully Email Sent');
            } else {
                $this->session->set_flashdata('flashError', 'Something Went Wrong..');
            }
        } 
        if($communication_type == 'sms' || $communication_type == 'both') {
            $userIds = array();
            foreach ($students as $key => $std) {
                $stdData = explode("_", $std);
                array_push($userIds, $stdData[4]);
            }
            $stdData = $this->user_provisioning_model->getActivationData($userIds);
            $part1 = $this->settings->getSetting('user_provisioning_manual_cridential_part_1');
            $part2 = $this->settings->getSetting('user_provisioning_manual_cridential_part_2');
            $school_code = $this->settings->getSetting('user_provisioning_school_code');
            // echo "<br><br><pre>"; print_r($stdData);die();
            $shIds_msg_mother = array();
            $shIds_msg_father = array();
            foreach($stdData as $key => $student){
                $randomString = $this->generatePassword(6);
                $set = $this->user_provisioning_model->activateUser($student->userId,$randomString);
                if($set) {
                    $message = "Dear ".$student->first_name . "," . $part1. "Ward Name: " . $student->stuName . "\nSchool code: ".$school_code."\nUser name: " . $student->username. "\nPassword: " . $randomString . $part2;
                    if($student->relation_type == 'Father') {
                        $shIds_msg_father[$student->student_id] = $message;
                    } else if($student->relation_type == 'Mother') {
                        $shIds_msg_mother[$student->student_id] = $message;
                    }
                }
            }
            
            $sh_type = 'Student';
            $source = 'User Provisioning';
            $sent_by = $this->authorization->getAvatarId();
            $sent_to_str = 'Student Individual';
            $ret1 = 1;
            $ret2 = 1;
            if(!empty($shIds_msg_father)) {
                $input_arr = array();
                $input_arr['student_id_messages'] = $shIds_msg_father;
                $input_arr['mode'] = 'sms';
                $input_arr['source'] = 'User Provisioning';
                $input_arr['send_to'] = 'Father';
                $res = $this->_send_unique_texts($input_arr);
                if($res['error'] != '') {
                    $this->session->set_flashdata('flashError', $res['error']);
                    redirect('user_provisioning_controller/get_studentData/'.$class.'/'.$sendTo.'/'.$state);
                }
                /*$ret1 = sendCustomMsg($shIds_msg_father, $sh_type, $source, '', $sent_by, $sent_to_str, 'Father');
                if($ret1 == -1){
                    $this->session->set_flashdata('flashError', 'Not enough credits available to send sms');
                    redirect('user_provisioning_controller/get_studentData/'.$class.'/'.$sendTo.'/'.$state);
                }*/
            }
            if(!empty($shIds_msg_mother)) {
                $input_arr = array();
                $input_arr['student_id_messages'] = $shIds_msg_mother;
                $input_arr['mode'] = 'sms';
                $input_arr['source'] = 'User Provisioning';
                $input_arr['send_to'] = 'Mother';
                $res = $this->_send_unique_texts($input_arr);
                if($res['error'] != '') {
                    $this->session->set_flashdata('flashError', $res['error']);
                    redirect('user_provisioning_controller/get_studentData/'.$class.'/'.$sendTo.'/'.$state);
                }
                /*$ret2 = sendCustomMsg($shIds_msg_mother, $sh_type, $source, '', $sent_by, $sent_to_str, 'Mother');
                if($ret2 == -1){
                    $this->session->set_flashdata('flashError', 'Not enough credits available to send sms');
                    redirect('user_provisioning_controller/get_studentData/'.$class.'/'.$sendTo.'/'.$state);
                }*/
            }
            $this->session->set_flashdata('flashSuccess', 'Successfully SMS Sent');
            /*if($ret1||$ret2) {
                $this->session->set_flashdata('flashSuccess', 'Successfully SMS Sent');
            } else {
                $this->session->set_flashdata('flashError', 'Something Went Wrong..');
            }*/
        }
        
        redirect('user_provisioning_controller/get_studentData/'.$class.'/'.$sendTo.'/'.$state);
    }

    public function deactivateUser(){
        echo $this->user_provisioning_model->deactivateUser($_POST['userId']);
    }

    public function manualActivate(){
        $userid = $this->input->post('userid');
        $randomString = $this->generatePassword(6);

        $result =  $this->user_provisioning_model->manualActivate($userid,$randomString);
        $part1 = $this->settings->getSetting('user_provisioning_manual_cridential_part_1');
        $part2 = $this->settings->getSetting('user_provisioning_manual_cridential_part_2');
        $school_code = $this->settings->getSetting('user_provisioning_school_code');

        $msg = "Dear ".$result->first_name . "," . $part1. "Ward Name: " . $result->stuName . "\nSchool code: ".$school_code."\nUser name: " . $result->username. "\nPassword: " . $randomString . $part2;
        $shIds_msg = array($result->student_id => $msg);
        /*$sh_type = 'Student';
        $source = 'User Provisioning';
        $sent_by = $this->authorization->getAvatarId();
        $sent_to_str = 'Student Individual';*/
        $ph_type = $this->input->post('relation');

        $input_arr = array();
        $input_arr['student_id_messages'] = $shIds_msg;
        $input_arr['mode'] = 'sms';
        $input_arr['source'] = 'User Provisioning';
        $input_arr['send_to'] = $ph_type;
        $res = $this->_send_unique_texts($input_arr);
        if($res['error'] != '') {
            echo 0;
        } else {
            echo 1;
        }
        /*$result->smsresult = sendCustomMsg($shIds_msg, $sh_type, $source, $msg, $sent_by, $sent_to_str, $ph_type);
        echo json_encode($result);*/
    }


    public function manualPasswordReset(){
        $userid = $this->input->post('userid');
        $randomString = $this->generatePassword(6);
        $result = $this->user_provisioning_model->manualPasswordReset($userid,$randomString);

        if (empty($result)) {
            echo 0;
        } else {
            // $msg = "Dear " . $result->first_name . ",\nYour password to School account is reset. User name: " . $result->username. "\nPassword: " . $randomString . ". Kindly change your password on your subsequent login. Do not forward this SMS to anybody.";
            $msg = "New credentials username:".$result->username." and Password:".$randomString;
            $shIds_msg = array($result->student_id => $msg);
            /*$sh_type = 'Student';
            $source = 'User Provisioning Password Reset';
            $sent_by = $this->authorization->getAvatarId();
            $sent_to_str = 'Student Individual';*/
            $ph_type = $this->input->post('relation');

            $input_arr = array();
            $input_arr['student_id_messages'] = $shIds_msg;
            $input_arr['mode'] = 'sms';
            $input_arr['source'] = 'User Provisioning';
            $input_arr['send_to'] = $ph_type;
            $res = $this->_send_unique_texts($input_arr);
            if($res['error'] != '') {
                echo 0;
            } else {
                echo 1;
            }
            /*$result->smsresult = sendCustomMsg($shIds_msg, $sh_type, $source, $msg, $sent_by, $sent_to_str, $ph_type);
            echo json_encode($result);*/
        }
    }

    public function provision_report() {
        if (!$this->authorization->isAuthorized('USER_MANAGEMENT.PROVISION_PARENTS')) {
            redirect('dashboard', 'refresh');
        }
        $report = $this->user_provisioning_model->getProvisionReport();
        $data['report'] = $report['clsReport'];
        $data['totalReport'] = $report['totalReport'];
        $data['main_content'] = 'user_provisioning/report';
        $this->load->view('inc/template', $data);
    }

    public function app_logins() {
        $this->load->library('filemanager');
        $this->load->model('student/Student_Model');
        $data['selectedClassId'] = $this->input->post('classId');
        $data['classList'] = $this->Student_Model->getClassNames();
        $data['classSectionList'] = $this->Student_Model->getClassSectionNames();
        $data['studentNames'] = $this->Student_Model->getstudentNames();
        if ($this->mobile_detect->isMobile()) {
            $data['main_content']    = 'user_provisioning/app_logins_mobile';
        } else {
            $data['main_content']    = 'user_provisioning/app_logins';
        }
        $this->load->view('inc/template', $data);
    }

    public function getStudentDetails(){
    $mode = $_POST['mode'];
    switch ($mode) {
      case 'section_id':
        $sectionId = $_POST['sectionId'];
        $stdData = $this->user_provisioning_model->getstdDataByClassSection($sectionId);
        break;
      case 'class_id':
        $classId = $_POST['classId'];
        $stdData = $this->user_provisioning_model->getstdDataByClass($classId);
        break;
      case 'name':
        $name = $_POST['name'];
        $stdData = $this->user_provisioning_model->getstdDataByName($name);
        break;
      case 'ad_no':
        $adNo = $_POST['ad_no'];
        $stdData = $this->user_provisioning_model->getStudentByAdNo($adNo);
        break;
      case 'phone_no':
        $phoneNo = $_POST['phone_no'];
        $stdData = $this->user_provisioning_model->getStudentByPhoneNo($phoneNo);
        break;
        case 'email':
        $email = $_POST['email'];
        $stdData = $this->user_provisioning_model->getStudentByEmail($email);
        break;
    }

    /*foreach ($stdData as &$std) {
      $std->admission_status_name = $this->settings->getSetting('admission_status')[$std->admission_status];
    }*/

    echo json_encode($stdData);
  }

  public function sendSms_student_details(){
        if (!$this->authorization->isAuthorized('USER_MANAGEMENT.PROVISION_PARENTS')) {
            redirect('dashboard', 'refresh');
        }
        $students = $_POST['students'];
        $messages = $_POST['messages'];
        $codes = $_POST['codes'];
        $process = $_POST['process'];
        $sendTo = $_POST['sendTo'];
        $class = $_POST['class'];
        $communication_type = $_POST['cm_type'];
        $state = $_POST['state'];
        $smsType = $_POST['smsType']; // sendSms or resendSms

        //Adding codes into user_prov_login_att table
        if($process == 'provision') {
            $this->_addProvision($codes, $smsType);
        }
        else if($process == 're-provision') {
            $this->user_provisioning_model->refreshAttempts($codes);
        }

        $parentEmails = [];
        $idMessages['Father'] = array();
        $idMessages['Mother'] = array();
        $userIds = array();
        foreach ($messages as $ids => $msg) {
            list($stdId, $pId, $relation, $user_id) = explode("_",$ids);
            $parentEmails[$pId] = $msg;
            $idMessages[$relation][$stdId] = $msg;
            array_push($userIds, $user_id);
        }
        if($process == 'activation') {
            $response = $this->user_provisioning_model->activateUsers($userIds);
            if($response == 0) {
                $this->session->set_flashdata('flashError', 'Unable to activate users');
                redirect('user_provisioning_controller/student_provisionbyid/'.$_POST['student_id']);
            }
        }

        if($communication_type == 'email' || $communication_type == 'both') {
            $status = $this->_communicateByEmail($parentEmails, $process);
            if($status == 1) {
                $this->session->set_flashdata('flashSuccess', 'Successfully Email Sent');
            } else {
                $this->session->set_flashdata('flashError', 'Something Went Wrong..');
            }
        } 
        if($communication_type == 'sms' || $communication_type == 'both'){
            $school = $this->settings->getSetting('school_short_name');
            // if($school == 'englishroots') {
                //$this->_communicateByNewSMS($parentEmails);
                // $status = 1;
            // } else {
                $status = $this->_communicateBySMS($idMessages, $process);
            // }
            if($status == 1) {
                $this->session->set_flashdata('flashSuccess', 'Successfully SMS Sent');
            } else if($status == 0){
                $this->session->set_flashdata('flashError', 'Something Went Wrong..');
            } else if($status == -1) {
                $this->session->set_flashdata('flashError', 'Not enough credits available');
            }
        }
        redirect('user_provisioning_controller/student_provisionbyid/'.$_POST['student_id']);
    }

    public function activateSMS_student_details(){
        if (!$this->authorization->isAuthorized('USER_MANAGEMENT.PROVISION_PARENTS')) {
            redirect('dashboard', 'refresh');
        }   
        $students = $_POST['students'];
        $sendTo = $_POST['sendTo'];
        $class = $_POST['class'];
        $state = $_POST['state'];
        $smsType = $_POST['smsType'];
        $communication_type = $_POST['cm_type'];

        if($communication_type == 'email' || $communication_type == 'both') {
            $status = $this->_communicateByEmail($students, $smsType, 'activation');
            if($status == 1) {
                $this->session->set_flashdata('flashSuccess', 'Successfully Email Sent');
            } else {
                $this->session->set_flashdata('flashError', 'Something Went Wrong..');
            }
        } 
        if($communication_type == 'sms' || $communication_type == 'both') {
            $userIds = array();
            foreach ($students as $key => $std) {
                $stdData = explode("_", $std);
                array_push($userIds, $stdData[4]);
            }
            $stdData = $this->user_provisioning_model->getActivationData($userIds);
            $part1 = $this->settings->getSetting('user_provisioning_manual_cridential_part_1');
            $part2 = $this->settings->getSetting('user_provisioning_manual_cridential_part_2');
            $school_code = $this->settings->getSetting('user_provisioning_school_code');
            // echo "<br><br><pre>"; print_r($stdData);die();
            $shIds_msg_mother = array();
            $shIds_msg_father = array();
            foreach($stdData as $key => $student){
                $randomString = $this->generatePassword(6);
                $set = $this->user_provisioning_model->activateUser($student->userId,$randomString);
                if($set) {
                    $message = "Dear ".$student->first_name . "," . $part1. "Ward Name: " . $student->stuName . "\nSchool code: ".$school_code."\nUser name: " . $student->username. "\nPassword: " . $randomString . $part2;
                    if($student->relation_type == 'Father') {
                        $shIds_msg_father[$student->student_id] = $message;
                    } else if($student->relation_type == 'Mother') {
                        $shIds_msg_mother[$student->student_id] = $message;
                    }
                }
            }
            
            $sh_type = 'Student';
            $source = 'User Provisioning';
            $sent_by = $this->authorization->getAvatarId();
            $sent_to_str = 'Student Individual';
            $ret1 = 1;
            $ret2 = 1;
            if(!empty($shIds_msg_father)) {
                $input_arr = array();
                $input_arr['student_id_messages'] = $shIds_msg_father;
                $input_arr['mode'] = 'sms';
                $input_arr['source'] = 'User Provisioning';
                $input_arr['send_to'] = 'Father';
                $res = $this->_send_unique_texts($input_arr);
                if($res['error'] != '') {
                    $this->session->set_flashdata('flashError', $res['error']);
                    redirect('user_provisioning_controller/student_provisionbyid/'.$_POST['student_id']);
                }
                /*$ret1 = sendCustomMsg($shIds_msg_father, $sh_type, $source, '', $sent_by, $sent_to_str, 'Father');
                if($ret1 == -1){
                    $this->session->set_flashdata('flashError', 'Not enough credits available to send sms');
                    redirect('user_provisioning_controller/get_studentData/'.$class.'/'.$sendTo.'/'.$state);
                }*/
            }
            if(!empty($shIds_msg_mother)) {
                $input_arr = array();
                $input_arr['student_id_messages'] = $shIds_msg_mother;
                $input_arr['mode'] = 'sms';
                $input_arr['source'] = 'User Provisioning';
                $input_arr['send_to'] = 'Mother';
                $res = $this->_send_unique_texts($input_arr);
                if($res['error'] != '') {
                    $this->session->set_flashdata('flashError', $res['error']);
                    redirect('user_provisioning_controller/student_provisionbyid/'.$_POST['student_id']);
                }
                /*$ret2 = sendCustomMsg($shIds_msg_mother, $sh_type, $source, '', $sent_by, $sent_to_str, 'Mother');
                if($ret2 == -1){
                    $this->session->set_flashdata('flashError', 'Not enough credits available to send sms');
                    redirect('user_provisioning_controller/get_studentData/'.$class.'/'.$sendTo.'/'.$state);
                }*/
            }
            $this->session->set_flashdata('flashSuccess', 'Successfully SMS Sent');
            /*if($ret1||$ret2) {
                $this->session->set_flashdata('flashSuccess', 'Successfully SMS Sent');
            } else {
                $this->session->set_flashdata('flashError', 'Something Went Wrong..');
            }*/
        }
        
       redirect('user_provisioning_controller/student_provisionbyid/'.$_POST['student_id']);
    }

}
