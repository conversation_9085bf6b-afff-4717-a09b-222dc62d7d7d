<?php

class Calendar_events_v2 extends CI_Controller
{

    function __construct()
    {
        parent::__construct();
        if (!$this->ion_auth->logged_in()) {
            redirect('auth/login', 'refresh');
        }

        if ($this->authorization->isModuleEnabled('CALENDAR_EVENTS_V2') && ($this->authorization->isAuthorized('CALENDAR_EVENTS_V2.MODULE'))) {
          $this->load->model('calendar_events_v2/Calendar_events_v2_model','Calendar_event_v2');
        } else {
          redirect('dashboard', 'refresh');
        }



    }

    public function index(){
        $site_url = site_url();
        $data['tiles'] = array(
            [
              'title' => 'Manage Calendar Templates',
              'sub_title' => 'Manage_calendar',
              'icon' => 'svg_icons/attendance.svg',
              'url' => $site_url.'calendar_events_v2/Calendar_events_v2/calendar_types',
              'permission' => $this->authorization->isAuthorized('CALENDAR_EVENTS_V2.MANAGE_CALENDAR')
            ],
            // [
            //   'title' => 'Assign Calendar',
            //   'sub_title' => 'Assign_calendar',
            //   'icon' => 'svg_icons/logout.svg',
            //   'url' => $site_url.'calendar_events_v2/Calendar_events_v2/map_calendar',
            //   'permission' => $this->authorization->isAuthorized('CALENDAR_EVENTS_V2.ASSIGN_CALENDAR')
            // ]

        );
        $data['tiles'] = checkTilePermissions($data['tiles']);

        $data['report_tiles'] = array(
            [
              'title' => 'Reports',
              'sub_title' => 'Reports',
              'icon' => 'svg_icons/daywiseclassattendance.svg',
              'url' => $site_url.'calendar_events_v2/Calendar_events_v2/reports_calendar',
              'permission' => $this->authorization->isAuthorized('CALENDAR_EVENTS_V2.REPORTS')
            ],
        );
        $data['report_tiles'] = checkTilePermissions($data['report_tiles']);

        $data['other_tiles'] = array(
            [
              'title' => 'Enable Config',
              'sub_title' => 'Enable_config',
              'icon' => 'svg_icons/attendancetemplate.svg',
              'url' => $site_url.'calendar_events_v2/Calendar_events_v2/admin_configs',
              'permission' => $this->authorization->isAuthorized('CALENDAR_EVENTS_V2.ENABLE_CONFIG')
            ],
        );

        $data['other_tiles'] = checkTilePermissions($data['other_tiles']);
        $data['main_content'] = 'calendar_events_v2/dashboard_desktop';
        $this->load->view('inc/template', $data);
      }

      public function calendar_types(){
        if (!$this->authorization->isAuthorized('CALENDAR_EVENTS_V2.MANAGE_CALENDAR')) {
          redirect('dashboard', 'refresh');
        }

        $data['acad_year_id'] =  $this->acad_year->getAllYearData();
        // echo "<pre>"; print_r($data['acad_year_id']); die();
        
        $data['title'] = 'Calendar EventsV2';
        if ($this->mobile_detect->isTablet()) {
          $data['main_content'] = 'staff/self_attendance/tablet_approve_neg.php';
        }else if($this->mobile_detect->isMobile()){
          $data['main_content'] = 'staff/self_attendance/mobile_checkin_neg';
        }else{
          $data['main_content'] = 'calendar_events_v2/calendar_templates';
        }
        $this->load->view('inc/template', $data);

      }

      public function add_events($id){
        if (!$this->authorization->isAuthorized('CALENDAR_EVENTS_V2.MANAGE_CALENDAR')) {
          redirect('dashboard', 'refresh');
        }
          $data['id']= $id;
          $data['attData']= $this->Calendar_event_v2->getAttDataIdWise($id);
          $data['title'] = 'Calendar EventsV2';
          if ($this->mobile_detect->isTablet()) {
            $data['main_content'] = 'staff/self_attendance/tablet_approve_neg.php';
          }else if($this->mobile_detect->isMobile()){
            $data['main_content'] = 'staff/self_attendance/mobile_checkin_neg';
          }else{
            $data['main_content'] = 'calendar_events_v2/add_events';
          }
          $this->load->view('inc/template', $data);
        }

        public function map_calendar(){

          if (!$this->authorization->isAuthorized('CALENDAR_EVENTS_V2.ASSIGN_CALENDAR')) {
            redirect('dashboard', 'refresh');
          }
          $data['calendarList']= $this->Calendar_event_v2->get_events_type();
          $data['title'] = 'Calendar EventsV2';
          if ($this->mobile_detect->isTablet()) {
            $data['main_content'] = 'staff/self_attendance/tablet_approve_neg.php';
          }else if($this->mobile_detect->isMobile()){
            $data['main_content'] = 'staff/self_attendance/mobile_checkin_neg';
          }else{
            $data['main_content'] = 'calendar_events_v2/map_calendar';
          }
          $this->load->view('inc/template', $data);

        }

      public function save_calendar_events(){
        if (!$this->authorization->isAuthorized('CALENDAR_EVENTS_V2.ENABLE_CONFIG')) {
          redirect('dashboard', 'refresh');
        }
        $input=$this->input->post();
        echo json_encode($this->Calendar_event_v2->save_calendar_events($input));
      }

      public function get_events(){
        if (!$this->authorization->isAuthorized('CALENDAR_EVENTS_V2.ENABLE_CONFIG')) {
          redirect('dashboard', 'refresh');
        }
        echo json_encode($this->Calendar_event_v2->get_events());
      }

     public function get_AttDataIdWise() {
      
        $calendarId = $this->input->post('calendar_id');
        $classSection = $this->input->post('classSection');
        $result = $this->Calendar_event_v2->getAttDataIdWise($calendarId,$classSection);
        // echo "<pre>"; print_r($result); die();

        echo json_encode(['status' => $result]);
    }

      public function get_events_display(){
        if (!$this->authorization->isAuthorized('CALENDAR_EVENTS_V2.ENABLE_CONFIG')) {
          redirect('dashboard', 'refresh');
        }
        echo json_encode($this->Calendar_event_v2->get_events_display());
      }


      public function save_calendar_name() {
        if (!$this->authorization->isAuthorized('CALENDAR_EVENTS_V2.ENABLE_CONFIG')) {
            redirect('dashboard', 'refresh');
        }
        $input = $this->input->post();
        $result = $this->Calendar_event_v2->save_calendar_name($input);
        echo json_encode($result);
      }

      public function get_events_type(){
        if (!$this->authorization->isAuthorized('CALENDAR_EVENTS_V2.ENABLE_CONFIG')) {
          redirect('dashboard', 'refresh');
        }
        echo json_encode($this->Calendar_event_v2->get_events_type());
      }

      public function get_class_calendar(){
        if (!$this->authorization->isAuthorized('CALENDAR_EVENTS_V2.ENABLE_CONFIG')) {
          redirect('dashboard', 'refresh');
        }
        echo json_encode($this->Calendar_event_v2->get_class_calendar());
      }

      public function get_staff_type(){
        if (!$this->authorization->isAuthorized('CALENDAR_EVENTS_V2.ENABLE_CONFIG')) {
          redirect('dashboard', 'refresh');
        }
        $result=$this->settings->getSetting('staff_type');
        echo json_encode($result);
      }

      public function calendar_class(){
        if (!$this->authorization->isAuthorized('CALENDAR_EVENTS_V2.ENABLE_CONFIG')) {
          redirect('dashboard', 'refresh');
        }
        echo json_encode($this->Calendar_event_v2->calendar_class());
      }
      public function calendar_staff_type(){
        if (!$this->authorization->isAuthorized('CALENDAR_EVENTS_V2.ENABLE_CONFIG')) {
          redirect('dashboard', 'refresh');
        }
        $input = $this->input->post();
        echo json_encode($this->Calendar_event_v2->calendar_staff_type($input));
      }

      public function update_calendar(){
        if (!$this->authorization->isAuthorized('CALENDAR_EVENTS_V2.MANAGE_CALENDAR')) {
          redirect('dashboard', 'refresh');
        }
        echo json_encode($this->Calendar_event_v2->update_calendar());

      }

      public function reports_calendar(){
        if (!$this->authorization->isAuthorized('CALENDAR_EVENTS_V2.REPORTS')) {
          redirect('dashboard', 'refresh');
        }
        $data['title'] = 'Calendar EventsV2';
        if ($this->mobile_detect->isTablet()) {
          $data['main_content'] = 'staff/self_attendance/tablet_approve_neg.php';
        }else if($this->mobile_detect->isMobile()){
          $data['main_content'] = 'staff/self_attendance/mobile_checkin_neg';
        }else{
          $data['main_content'] = 'calendar_events_v2/calendar_report';
        }
          $this->load->view('inc/template', $data);
      }


      public function admin_configs(){
        if (!$this->authorization->isAuthorized('CALENDAR_EVENTS_V2.ENABLE_CONFIG')) {
          redirect('dashboard', 'refresh');
        }
        $data['title'] = 'Calendar EventsV2';
        if ($this->mobile_detect->isTablet()) {
          $data['main_content'] = 'staff/self_attendance/tablet_approve_neg.php';
        }else if($this->mobile_detect->isMobile()){
          $data['main_content'] = 'staff/self_attendance/mobile_checkin_neg';
        }else{

          $data['main_content'] = 'calendar_events_v2/admin_config';
        }
          $this->load->view('inc/template', $data);

      }

      public function get_calendar_detail() {
        if (!$this->authorization->isAuthorized('CALENDAR_EVENTS_V2.MANAGE_CALENDAR')) {
            echo json_encode(['status' => 'error', 'message' => 'Unauthorized access']);
            return;
        }

        $calendar_id = $this->input->post('calendar_id');
        $result = $this->Calendar_event_v2->get_calendar_detail($calendar_id);
        echo json_encode($result);
      }

      public function map_calender_template($id){
        $data['id']= $id;
        $data['title'] = 'Calendar EventsV2';
        if ($this->mobile_detect->isTablet()) {
          $data['main_content'] = 'staff/self_attendance/tablet_approve_neg.php';
        }else if($this->mobile_detect->isMobile()){
          $data['main_content'] = 'staff/self_attendance/mobile_checkin_neg';
        }else{
          $data['main_content'] = 'calendar_events_v2/map_calender_template';
        }
        $this->load->view('inc/template', $data);
      }

      public function get_selected_staff_types() {
        if (!$this->authorization->isAuthorized('CALENDAR_EVENTS_V2.ENABLE_CONFIG')) {
            redirect('dashboard', 'refresh');
        }
        $calendar_id = $this->input->post('calendar_id');
        $result = $this->Calendar_event_v2->get_selected_staff_types($calendar_id);
        echo json_encode($result);
      }

      public function deleteCalandarTemplate() {
        if (!$this->authorization->isAuthorized('CALENDAR_EVENTS_V2.MANAGE_CALENDAR')) {
            echo json_encode(['status' => 'error', 'message' => 'Unauthorized access']);
            return;
        }

        $result = $this->Calendar_event_v2->deleteCalandarTemplate();
        echo json_encode($result);
    }

    public function get_mapping_info() {
        if (!$this->authorization->isAuthorized('CALENDAR_EVENTS_V2.MANAGE_CALENDAR')) {
            echo json_encode(['error' => 'Unauthorized access']);
            return;
        }

        $calendar_id = $this->input->post('calendar_id');
        if (!$calendar_id) {
            echo json_encode(['error' => 'Invalid calendar ID']);
            return;
        }

        $result = $this->Calendar_event_v2->get_mapping_info($calendar_id);
        echo json_encode($result);
    }

    public function delete_event() {
        if (!$this->authorization->isAuthorized('CALENDAR_EVENTS_V2.ENABLE_CONFIG')) {
            echo json_encode(['status' => 'error', 'message' => 'Unauthorized access']);
            return;
        }
        $event_id = $this->input->post('event_id');
        if (!$event_id) {
            echo json_encode(['status' => 'error', 'message' => 'Invalid event ID']);
            return;
        }
        $result = $this->Calendar_event_v2->delete_event($event_id);
        echo json_encode($result);
    }

    public function delete_staff_type() {
        if (!$this->authorization->isAuthorized('CALENDAR_EVENTS_V2.ENABLE_CONFIG')) {
            echo json_encode(['status' => 'error', 'message' => 'Unauthorized access']);
            return;
        }

        $input = $this->input->post();
        $result = $this->Calendar_event_v2->delete_staff_type($input);
        echo json_encode($result);
    }

    public function delete_class_section() {
        if (!$this->authorization->isAuthorized('CALENDAR_EVENTS_V2.ENABLE_CONFIG')) {
            echo json_encode(['status' => 'error', 'message' => 'Unauthorized access']);
            return;
        }

        $input = $this->input->post();
        $result = $this->Calendar_event_v2->delete_class_section($input);
        echo json_encode($result);
    }

    public function save_mapping() {
        if (!$this->authorization->isAuthorized('CALENDAR_EVENTS_V2.ENABLE_CONFIG')) {
            echo json_encode(['status' => 'error', 'message' => 'Unauthorized access']);
            return;
        }

        $input = $this->input->post();
        if (empty($input['calendar_id'])) {
            echo json_encode(['status' => 'error', 'message' => 'Calendar ID is required']);
            return;
        }

        $result = $this->Calendar_event_v2->save_mapping($input);
        echo json_encode($result);
    }


    public function lock_calendar_template(){
      if (!$this->authorization->isAuthorized('CALENDAR_EVENTS_V2.MANAGE_CALENDAR')) {
          echo json_encode(['status' => 'error', 'message' => 'Unauthorized access']);
          return;
      }

      $result = $this->Calendar_event_v2->lock_calendar_template();
      echo json_encode($result);
    }

    public function update_event() {
        if (!$this->authorization->isAuthorized('CALENDAR_EVENTS_V2.ENABLE_CONFIG')) {
            echo json_encode(['status' => 'error', 'message' => 'Unauthorized access']);
            return;
        }
        $input = $this->input->post();
        $result = $this->Calendar_event_v2->update_event($input);
        echo json_encode($result);
    }

    public function clone_calendar_template() {
        if (!$this->authorization->isAuthorized('CALENDAR_EVENTS_V2.MANAGE_CALENDAR')) {
            echo json_encode(['status' => 'error', 'message' => 'Unauthorized access']);
            return;
        }
        $calendar_id = $this->input->post('calendar_id');
        if (!$calendar_id) {
            echo json_encode(['status' => 'error', 'message' => 'Invalid calendar ID']);
            return;
        }
        $result = $this->Calendar_event_v2->clone_calendar_template($calendar_id);
        echo json_encode($result);
    }

}
