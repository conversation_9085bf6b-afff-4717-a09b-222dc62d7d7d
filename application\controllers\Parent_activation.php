<?php defined('BASEPATH') OR exit('No direct script access allowed');

class Parent_activation extends CI_Controller {

	public function __construct()	{
        parent::__construct();

		if (!$this->ion_auth->logged_in()) {
            redirect('auth/login', 'refresh');
        }

        $this->load->model('user_provisioning_model');
        $this->load->model('parent_activation_model');
        $this->load->model('student/Student_Model');
        $this->config->load('form_elements');
    }

    public function index(){
       if (!$this->authorization->isAuthorized('USER_MANAGEMENT.PROVISION_PARENTS')) {
            redirect('dashboard', 'refresh');
        }   
        // $data['classSectionList'] = $this->Student_Model->getClassSectionNames();
        $yearId = $this->acad_year->getAcadYearId();
        $data['classSectionList'] = $this->user_provisioning_model->getClassSectionNames($yearId);
        $data['classSectionId'] = 0;
        // echo '<pre>'; print_r(json_encode($data['classSectionList'])); die();
        $data['sendTo'] = 'Both';
        $data['status'] = 'All';
        $data['student_name'] = '';
        $data['main_content'] = 'parent_activation/index';
        $this->load->view('inc/template', $data);
    }


    public function get_credentials_student_ids_list_by_filter(){
        $classSectionId  =$_POST['classSectionId'];
        $student_name  =$_POST['student_name'];
        $studentIds = $this->parent_activation_model->get_provision_student_list_by_filter($classSectionId, $student_name);
        $studentId = array_chunk($studentIds, 20);
        echo json_encode($studentId);
    }

    public function get_credentials_student_data(){
        $student_ids = $_POST['student_ids'];
        $result = $this->parent_activation_model->getProvisionStudentDetails($student_ids);
        echo json_encode($result);
    }

    public function deactivate_provision_credentials_by_user_id(){
        $userIds = $_POST['userIds'];
        echo $this->parent_activation_model->deactivate_provision_credentials_by_user_id($userIds);
    }

    public function getPreview_credentials(){
        if (!$this->authorization->isAuthorized('USER_MANAGEMENT.PROVISION_PARENTS')) {
            redirect('dashboard', 'refresh');
        }

        $pids = $_POST['pids'];
        $conf_msg = $this->settings->getSetting('user_provisioning_link_message_body');
        $school = $this->settings->getSetting('school_short_name');
        $msg_footer = $this->settings->getSetting('user_provisioning_link_message_footer');
        $part1 = $this->settings->getSetting('user_provisioning_manual_cridential_part_1');
        $part2 = $this->settings->getSetting('user_provisioning_manual_cridential_part_2');

        $existingCodes = $this->user_provisioning_model->getExistingCodes();
        $pData = array();
        foreach ($pids as $key => $val) {
            $explodeVal = explode('_', $val);
            $student = $this->parent_activation_model->getPreviewCredentialsData($explodeVal[0]);

            if ($explodeVal[1] == 'email') {
                $email_content = $this->user_provisioning_model->get_user_provision_email_template();
            }

            if($explodeVal[1] == 'sms')
                $message_by = $student->mobile;
            else if($explodeVal[1] == 'email')
                $message_by = $student->email;

            $message_for_credit_calculation = '';
            $name = "$student->studentName";
            $encId = $this->generateRandomCode(6);

            if($_POST['process'] == 'provision') {
                //generating random code for url
                do {
                    $encId = $this->generateRandomCode(6);
                }while(in_array($encId, $existingCodes));
                array_push($existingCodes, $encId);
                $url = site_url('user_provisioning_controller/verifyMobile/').$encId;
                $message = "Dear $name,\n$conf_msg Please click on the following link - $url , to activate your account. This is a personalised link. DO NOT share this link with anybody.\n$msg_footer.";

            } else {
                $encId = 0;
                $randomString = $this->generatePassword(6);
                $res = $this->user_provisioning_model->resetPassword($student->user_id,$randomString);
                $message = str_replace("%%username%%", $student->username, $part1);
                $message = str_replace("%%password%%", $randomString, $message);

                if ($explodeVal[1] == 'email') {
                    $message = str_replace("%%student_name%%", $name, (empty($email_content)) ? '' : $email_content->content);
                    $message = str_replace("%%username%%", $student->username, $message);
                    $message = str_replace("%%password%%", $randomString, $message);
                }
            }

            if(strlen($message_for_credit_calculation) < strlen($message)) {
                $message_for_credit_calculation = $message;
                //get the largest message for credits calculation
            }

            $pData[] = array(
                'std_id' => $student->std_id,
                'pid' => $student->pid,
                'user_id' => $student->user_id,
                'relation_type' => $student->relation_type,
                'name' => $name,
                'message' => $message,
                'message_by' => $message_by,
                'code' => $encId,
                'send_type' =>$explodeVal[1]
            );

            $is_credits_available = 1;
            if($explodeVal[1] == 'sms') {
                $this->load->helper('texting_helper');
                $is_credits_available = checkCredits($message_for_credit_calculation, count($pData), 'parent');
            }
        }
        echo json_encode(array('preview' => $pData, 'credits_available' => $is_credits_available));
    }

    private function generateRandomCode($length = 6) {
        return substr(str_shuffle(str_repeat($x='ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz1234567890', ceil($length/strlen($x)) )),1,$length);
    }

    public function get_studentData() {
        if (!$this->authorization->isAuthorized('USER_MANAGEMENT.ACTIVATE_PARENTS')) {
            redirect('dashboard', 'refresh');
        }   
        $data['classSectionId'] = 0;
        $data['std_name'] = '';
        
        if(isset($_POST['classSectionId']))
            $data['classSectionId'] = $_POST['classSectionId'];

        if(isset($_POST['std_name']))
            $data['std_name'] = $_POST['std_name'];
        
        if($data['std_name'] == '') {
            $data['getstudents'] = $this->parent_activation_model->getstudentDetails($data['classSectionId']);
        } else {
            $data['getstudents'] = $this->parent_activation_model->getstudentDetailsByName($data['std_name']);
        }
        // echo "<pre>"; print_r($data); die();
        foreach ($data['getstudents'] as $key => $student) {
            $student->status = 'Not Activated';
            if($student->Active == 1){
                $student->status = 'Activated';
                if($student->loggedin_atleast_once) {
                    $student->status = 'LoggedIn';
                }
            }
        }
        $data['studentNames'] = $this->Student_Model->getstudentNames();
        $data['classSectionList'] = $this->Student_Model->getClassSectionNames();
        $data['main_content'] = 'parent_activation/index';
        $this->load->view('inc/template', $data);
    }

    private function generatePassword($length = 10) {
        return substr(str_shuffle(str_repeat($x='abcdefghijkmnopqrstuvwxyz', ceil($length/strlen($x)) )),1,$length);
    }

    public function getActivationPreview() {
        if (!$this->authorization->isAuthorized('USER_MANAGEMENT.ACTIVATE_PARENTS')) {
            redirect('dashboard', 'refresh');
        }   
        $students = $this->parent_activation_model->getPreviewData();
        $activation_message = $this->settings->getSetting('user_activation_message_new'); 
        $re_activation_message = $this->settings->getSetting('user_reactivation_message_new');
        
        $pData = array();
        foreach($students as $student){
            if($_POST['ctype'] == 'sms')
                $message_by = $student->mobile;
            else if($_POST['ctype'] == 'email')
                $message_by = $student->email;
            else if($_POST['ctype'] == 'both')
                $message_by = "$student->email ($student->mobile)";

            //checking if parent dont have mobile or email or both 
            if($message_by == '' || preg_match('/^\s\(\)$/',$message_by)) {
                continue;
            }
            
            $message_for_credit_calculation = '';

            $name = "$student->parentName ($student->relation_type of $student->studentName),\n";
            $encId = 0;
            $randomString = $this->generatePassword(6);
            $res = $this->user_provisioning_model->resetPassword($student->user_id,$randomString);
            if($_POST['process'] == 'activation') {
                $message = str_replace("%%name%%", $name, $activation_message);
                $credential = "\nCredentials -\nUsername: $student->username\nPassword: $randomString.\n";
                $message = str_replace("%%credentials%%", $credential, $message);
            } else {
                $message = str_replace("%%name%%", $name, $re_activation_message);
                $credential = "\nCredentials -\nUsername: $student->username\nPassword: $randomString.\n";
                $message = str_replace("%%credentials%%", $credential, $message);
            }

            if(strlen($message_for_credit_calculation) < strlen($message)) {
                $message_for_credit_calculation = $message;
                //get the largest message for credits calculation
            }
            
            $pData[] = array(
                'std_id' => $student->std_id,
                'pid' => $student->pid,
                'user_id' => $student->user_id,
                'relation_type' => $student->relation_type,
                'name' => $name,
                'message' => $message,
                'message_by' => $message_by,
                'code' => $encId
            );
        }

        $is_credits_available = 1;
        if($_POST['ctype'] == 'sms' || $_POST['ctype'] == 'both') {
            $this->load->helper('texting_helper');
            $is_credits_available = checkCredits($message_for_credit_calculation, count($pData), 'parent');
        }
        echo json_encode(array('preview' => $pData, 'credits_available' => $is_credits_available));
    }

    public function send_parent_provision_credentials(){
        $parentEmails = [];
        $messages = $_POST['messages'];
        $idMessages['Father'] = array();
        $idMessages['Mother'] = array();
        $userIds = array();
        foreach ($messages as $ids => $msg) {
            list($stdId, $pId, $relation, $user_id, $sent_type) = explode("_",$ids);
            if ($sent_type =='email' ) {
                $parentEmails[$pId] = $msg;
            }else{
                $idMessages[$relation][$stdId] = $msg;
            }
            array_push($userIds, $user_id);
        }
        $activeResponse = $this->user_provisioning_model->activateUsers($userIds);
        if($activeResponse == 0) {
            $this->session->set_flashdata('flashError', 'Unable to activate users');
            redirect('parent_activation');
        }
        if (!empty($parentEmails)) {
            $emailStatus = $this->_communicateByEmail($parentEmails);
            if($emailStatus == 1) {
                $this->session->set_flashdata('flashSuccess', 'Successfully Email Sent');
            } else {
                $this->session->set_flashdata('flashError', 'Something Went Wrong..');
            }
        }
        if (!empty($idMessages['Father']) || !empty($idMessages['Mother'])) {
             $smsStatus = $this->_communicateBySMS($idMessages);
            if($smsStatus == 1) {
               $this->session->set_flashdata('flashSuccess', 'Successfully SMS Sent');
            } else if($smsStatus == 0){
                $this->session->set_flashdata('flashError', 'Something Went Wrong..');
            } else if($smsStatus == -1) {
                $this->session->set_flashdata('flashError', 'Not enough credits available');
            }
        }
        redirect('parent_activation');
    }

    public function send_parent_provision_credentials_from_admissions(){
        $parentEmails = [];
        $messages = $_POST['messages'];
        $idMessages['Father'] = array();
        $idMessages['Mother'] = array();
        $userIds = array();
        foreach ($messages as $ids => $msg) {
            list($stdId, $pId, $relation, $user_id, $sent_type) = explode("_",$ids);
            if ($sent_type =='email' ) {
                $parentEmails[$pId] = $msg;
            }else{
                $idMessages[$relation][$stdId] = $msg;
            }
            array_push($userIds, $user_id);
        }
        $activeResponse = $this->user_provisioning_model->activateUsers($userIds);
        $result = '0';
        $message = '';
        if($activeResponse == 0) {
            $result = '0';
            $message = 'Unable to activate users';
        }
        
        if (!empty($parentEmails)) {

            $emailStatus = $this->_communicateByEmail($parentEmails);
            if($emailStatus == 1) {
                $result = '1';
                $message = 'Successfully Email Sent';
            } else {
                $result = '0';
                $message = 'Something Went Wrong..';
            }
        }
        if (!empty($idMessages['Father']) || !empty($idMessages['Mother'])) {
             $smsStatus = $this->_communicateBySMS($idMessages);
            if($smsStatus == 1) {
                $result = '1';
                $message = 'Successfully SMS Sent';
            } else if($smsStatus == 0){
                $result = '0';
                $message = 'Something Went Wrong..';
            } else if($smsStatus == -1) {
                $result = '-1';
                $message = 'Not enough credits available';
            }
        }
        echo json_encode(array('result'=>$result,'message'=>$message));
    }

    private function _communicateByEmail($parentEmails) {
        if(empty($parentEmails)){
            return 0;
        }

        $parentIds = array();

        foreach ($parentEmails as $pid => $pEmail) {
            $parentIds[] = $pid;
        }

        if(empty($parentIds)){
            return 0;
        }

        $emails = $this->user_provisioning_model->getEmailsByParentId($parentIds);

        if(empty($emails)){
            return 0;
        }

        $mailIds = array();
        $messages = array();
        $usernames = array();
        $emailStore = array();
        foreach ($emails as $key => $email) {
            array_push($mailIds, $email->email);
            array_push($usernames, $email->username);
            array_push($messages, $parentEmails[$email->parentId]);
            $emailStore[$email->parentId] = array(
                'email_body'=>$parentEmails[$email->parentId],
                'email'=>$email->email,
                'avatar_type'=>$email->avatar_type,
                'stakeholder_id'=>$email->parentId
            ) ;
        }

        $set = $this->user_provisioning_model->get_user_provision_email_template();

        if(empty($set)){
            return 0;
        }

        $this->load->model('communication/emails_model');
        $this->load->helper('email_helper');

        foreach ($emailStore as $parentId => $email) {
            $email_ids = array();

            $email_master_data = array(
                'subject' => (empty($set)) ? '' : $set->email_subject,
                'body' => $email['email_body'],
                'source' => 'Provision Parent Credentails',
                'sent_by' => $this->authorization->getAvatarId(),
                'recievers' => 'Parents',
                'from_email' => $set->registered_email,
                'files' => '',
                'acad_year_id' => $this->acad_year->getAcadYearId(),
                'visible' => 1,
                'sender_list' => NULL,
                'sending_status' => 'Completed',
            );
            $email_master_id = $this->emails_model->saveEmail($email_master_data);

            $email_obj = new stdClass();
            $email_obj->stakeholder_id = $email['stakeholder_id'];
            $email_obj->avatar_type = $email['avatar_type'];
            $email_obj->email = trim($email['email']);
            $email_data = [$email_obj];

            if(!empty(trim($email['email']))){
                $email_ids = [$email['email']];
            }
            $this->emails_model->save_sending_email_data($email_data, $email_master_id);
            if(!empty($email_ids)){
                sendEmail($email['email_body'], $set->email_subject, $email_master_id, $email_ids, $set->registered_email);
            }
        }
        return 1;
    }

    public function _emailSender_provision($data){
        $set = $this->user_provisioning_model->get_user_provision_email_template();  
        $from_name = $this->settings->getSetting('school_name');     
        $from_email = (empty($set)) ? '' : $set->registered_email;
         // echo "<pre>"; print_r($from_email);die();
        $smtp_user = CONFIG_ENV['smtp_user'];
        $smtp_pass = urlencode(CONFIG_ENV['smtp_pass']);
        $smtp_host = CONFIG_ENV['smtp_host'];
        $smtp_port = CONFIG_ENV['smtp_port'];

        $data['from_email'] = $from_email;
        $data['from_name'] = $from_name;
        $data['smtp_user'] = $smtp_user;
        $data['smtp_pass'] = $smtp_pass;
        $data['smtp_host'] = $smtp_host;
        $data['smtp_port'] = $smtp_port;
        $data = http_build_query($data);
        $curl = curl_init();

        $username = CONFIG_ENV['job_server_username'];
        $password = CONFIG_ENV['job_server_password'];
        curl_setopt_array($curl, array(
            CURLOPT_URL => CONFIG_ENV['job_server_unique_email_uri'],
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_USERPWD => $username . ":" . $password,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "POST",
            CURLOPT_POST => 1,
            CURLOPT_POSTFIELDS => $data,
            CURLOPT_HTTPHEADER => array(
                "Accept: application/json",
                "Cache-Control: no-cache",
                "Content-Type: application/x-www-form-urlencoded",
                "Postman-Token: 090abdb9-b680-4492-b8b7-db81867b114e"
            ),
        ));

        $response = curl_exec($curl);

        $err = curl_error($curl);

        curl_close($curl);

        if ($err) {
          return 0;
        } else {
          return 1;
        }
    }

    private function _communicateBySMS($stdMessages) {
        $sh_type = 'Student';
        $source = 'Parent User Provisioning';
       
        $sent_by = $this->authorization->getAvatarId();
        $sent_to_str = 'Student Individual';
        if(!empty($stdMessages['Father'])) {
            $input_arr = array();
            $input_arr['student_id_messages'] = $stdMessages['Father'];
            $input_arr['mode'] = 'sms';
            $input_arr['source'] = 'User Provisioning';
            $input_arr['send_to'] = 'Father';
            $res = $this->_send_unique_texts($input_arr);
            if($res['error'] != '') {
                return 0;
            }
        }

        if(!empty($stdMessages['Mother'])) {
            $input_arr = array();
            $input_arr['student_id_messages'] = $stdMessages['Mother'];
            $input_arr['mode'] = 'sms';
            $input_arr['source'] = 'User Provisioning';
            $input_arr['send_to'] = 'Mother';
            $res = $this->_send_unique_texts($input_arr);
            if($res['error'] != '') {
                return 0;
            }
        }
        return 1;
    }

    private function _send_unique_texts($input) {
        $this->load->helper('texting_helper');
        return sendUniqueText($input);
    }

    public function reset_default_password_user_id(){
        $userId = $_POST['userId'];
        $data = array(
            'password' => 'welcome123',
            );
        echo $this->ion_auth->update($userId, $data);
    }
}