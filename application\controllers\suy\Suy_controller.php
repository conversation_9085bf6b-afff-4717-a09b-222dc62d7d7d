
<?php
class Suy_controller extends CI_Controller {
	function __construct() {
    parent::__construct();
    if (!$this->ion_auth->logged_in()) {
      redirect('auth/login', 'refresh');
    }
    if (!$this->authorization->isModuleEnabled('INDUS_SUY') && !$this->authorization->isAuthorized('INDUS_SUY.MODULE')) {
      redirect('dashboard', 'refresh');
    }  
    $this->load->model('suy/Suy_model');
    $this->load->model('student/Student_Model');
    $this->load->model('feesv2/fees_student_model');
    $this->load->model('feesv2/fees_cohorts_model');
    $this->load->model('feesv2/fees_collection_model');
    $this->load->library('payment');
    $this->load->model('Admission_model');
    $this->load->model('user_provisioning_model');
    $this->load->model('parent_activation_model');
  }

  public function suy_manage_page() {
    $data['main_content']    = 'suy/suy_enquiry_page/index';
    $this->load->view('suy/inc/template', $data);
  }

  public function create_suy_enquiry_page() {
    $data['main_content']    = 'suy/suy_enquiry_page/create_suy_enquiry_form';
    $this->load->view('inc/template', $data);
  }

  public function registered_student_page(){
    $data['classList'] = $this->Suy_model->getClassNames();
    $data['main_content']    = 'suy/registered_student_page';
    $this->load->view('inc/template', $data);
  }
  
  public function insert_enquiry_data(){
    $result = $this->Suy_model->insert_enquiry_data();
     redirect('suy-success');
  }

  public function create_enquiry_form(){
    $result = $this->Suy_model->create_enquiry_form();
    // echo '<pre>';print_r($result);die();
    if($result){
      $this->session->set_flashdata('flashSuccess', 'Successfully inserted');
      redirect('suy/suy_controller/create_suy_enquiry_page');
    }else{
      $this->session->set_flashdata('flashError', 'Something went wrong');
    }
    
  }
  public function suy_success_page() {
    $data['main_content']    = 'suy/suy_enquiry_page/suy_success_page';
    $this->load->view('suy/inc/template', $data);
  }
  public function suy_enquiry_page() {
    $data['student_uid'] = $this->uri->segment(4);
    $data['grades'] =  $this->Suy_model->get_all_grades();
    $data['follow_up_status'] = json_decode($this->settings->getSetting('enquiry_follow_up_status'));
    // $data['fees'] =$this->fees_student_model->get_fee_assigned_details($data['student_uid']);
    $data['fee_details'] =$this->fees_student_model->get_all_prints_fee_details();
    // echo "<pre>"; print_r($data['fee_details']); die();
    $data['main_content']    = 'suy/suy_enquiry_page/suy_enquiry_data';
    $this->load->view('inc/template', $data);
  }
 public function get_fees_details() {
  $std_adm_id = $_POST['std_admit_id'];
  $fees =$this->fees_student_model->get_fee_assigned_details($std_adm_id);
  $link_status =$this->Suy_model->get_link_status($std_adm_id);
  // echo '<pre>';print_r($link_status);die();
  echo json_encode(array('fees'=>$fees,'link_status'=>$link_status));
 }

  public function suy_enquiry_detailsbyId(){
    $enquiryId = $_POST['enquiryId'];
    $got_to_know_by = $this->settings->getSetting('enquiry_how_did_you_get_to_know_about_us');
    $course = ['Select course','Beginner'=>'Beginner','Intermediate'=>'Intermediate','Advanced'=>'Advanced'];
    $classes = $this->Student_Model->getClassByAcadYear(22);
    $follow_up_status = json_decode($this->settings->getSetting('enquiry_follow_up_status'));
    $follow_enuiry = $this->Suy_model->get_enquiry_v2_byId($enquiryId);
    // echo '<pre>';print_r($data['suy_status']);die();
    echo json_encode(array('follow_up_status'=>$follow_up_status,'follow_enuiry'=>$follow_enuiry,'got_to_know_by'=>$got_to_know_by,'course'=>$course,'classes'=>$classes
                           ));
  }
  public function get_followup_action_popup_details(){
    $follow_up_type = $this->input->post('follow_up_type');
    $follow_up_action = $this->input->post('follow_up_action');
    $source_id = $this->input->post('source_id');
    $result = $this->Suy_model->get_followup_action_popup_details($follow_up_type, $follow_up_action, $source_id);
    echo json_encode($result);
  }

  public function get_suy_enquiry_data(){
   
    $createdfrom_date = $_POST['createdfrom_date'];
    $createdto_date = $_POST['createdto_date'];
    $followupfrom_date = $_POST['followupfrom_date'];
    $followupto_date = $_POST['followupto_date'];
    $follow_up_status = $_POST['follow_up_status'];
    $grade = $_POST['grade'];
    // echo '<pre>';print_r($grade);die();

    $enquiries =  $this->Suy_model->get_enquiry_follow_data_v2($createdfrom_date,$createdto_date,$followupfrom_date,$followupto_date,$follow_up_status, $grade);
    echo json_encode($enquiries);
  }
  public function follow_up_details_insert(){
    $input = $this->input->post();
    $enquiry_id = $input['enquiry_id'];
    $result = $this->Suy_model->update_suy_enquiry_follow_up_data($enquiry_id, $input);
    if($result){
      $this->session->set_flashdata('flashSuccess', 'Successfully Created');
    }else{
      $this->session->set_flashdata('flashError', 'Something went wrong');
    }
    echo $result;
  }
  public function updateEnquiryData() {
    $input = $_POST;
    // echo '<pre>';print_r($input);die();
    $value =  $_POST['value'];
    $name =  $_POST['id'];
    $enquiry_id =  $_POST['enquiry_id'];
    $this->Suy_model->updateEnquiryData($enquiry_id, $name, $value);
    echo $value;
  }
  function saveFieldValue() {
    echo $this->Suy_model->saveFieldValue();
  }
  function move_student_data_to_temp() {
    $classId =  $_POST['class_name'];
    $sectionId =  $_POST['classSection'];
    $suyEnquiryid =  $_POST['suyEnquiryid'];
    $parent_type =  $_POST['parent_type'];
    // echo '<pre>';print_r($parent_type);die();
    $suy_stddata = $this->Suy_model->get_suy_stddata_byId($suyEnquiryid);
    $stdData = $this->_constrcut_student_array_temp($suy_stddata,$classId,$sectionId);
    $config_admission_number = $this->settings->getSetting('admission_number');
      $lastRecord = $this->Student_Model->getLastStudentid(); 
      if (!$lastRecord) {
        $lastRecordId = 1;
      } else {
        $lastRecordId = ($lastRecord->id + 1);
      }
      $params['number'] = sprintf("%0".$config_admission_number->digit_count . "s", $lastRecordId);
      $params['year_of_joining'] = '22';
      $params['classid'] = $this->Student_Model->getClassByID($classId);   
      $stdData['admission_no'] = $this->_generateAdmissionNo($config_admission_number,$params);

    $this->db->trans_begin();
    $student_uid = $this->Student_Model->addStudentInfo($stdData, $patth=array('file_name'=>''));
    $std_adm_id = $student_uid['stdAdmId'];
    // echo '<pre>';print_r($student_uid);die();
    if($student_uid == 0) {
      echo 0; // Failed to Insert Student details
      return;
    }else{
      // if($parent_type == 'Father'){
      //   $fatherData = $this->_constrcut_father_array($suy_stddata);       
      // }else{
      //   $motherData = $this->_constrcut_mother_array($suy_stddata);
      // }
      $fatherData = $this->_constrcut_father_array($suy_stddata, $parent_type);       
      $motherData = $this->_constrcut_mother_array($suy_stddata, $parent_type);

      $father_uid = $this->Student_Model->addParentInfo($fatherData,$student_uid['stdAdmId'],'Father',$patth=array('file_name'=>''), $stdData['student_firstname']);
        if (!$father_uid) {
            echo 0; // Failed to Insert Father details
            return;
        }
      $mother_uid = $this->Student_Model->addParentInfo($motherData,$student_uid['stdAdmId'],'Mother',$patth=array('file_name'=>''), $stdData['student_firstname']);
        if (!$mother_uid) {
          echo 0; // Failed to Insert Mother details
            return;
        }
    }
    //update suy_enquiry status
    if ($this->db->trans_status()) {
        $this->db->trans_commit();

        $suy_update = $this->Suy_model->updater_suy_enquiry($suyEnquiryid,$student_uid['stdAdmId']);
          echo 1;
          // return $std_adm_id;
        } else {
          echo 0;
        }
  }

  private function _constrcut_student_array_temp($suy_stddata,$classId,$sectionId){
    $stdData = array();
    $stdData['admission_acad_year'] = 22;
    $stdData['student_firstname'] = $suy_stddata->student_first_name;
    $stdData['student_lastname'] = $suy_stddata->student_last_name;
    $stdData['student_dob'] = '';
    $stdData['course_name'] = $suy_stddata->course_name;
    $stdData['gender'] = '';
    $stdData['nationality'] = '';
    $stdData['religion'] =  '';
    $stdData['category'] = '';
    $stdData['mother_tongue'] =  ''; 
    $stdData['rteid'] = 2;
    // $stdData['class_name'] = $suy_stddata->class_name;   
    // $stdData['mobile_no'] = $suy_stddata->parent_mobile_number; 
    $stdData['student_doj'] = null;
    $stdData['birth_taluk'] = '';
    $stdData['birth_district'] = '';
    $stdData['caste'] ='';
    $stdData['std_aadhar'] = ''; 
    $stdData['class_admitted_to'] = null;
    $stdData['admission_year'] = null;
    $stdData['admission_acad_year_id'] = 22;
    $stdData['roll_no'] = null;
    $stdData['classid'] = $classId;
    $stdData['classsection'] = $sectionId;
    $stdData['boardingid'] = '1'; 
    $stdData['file_name'] = '';
    $stdData['acad_year'] = '22';
    $stdData['board'] = '';
    $stdData['medid'] = '';
    $stdData['acad_year_id'] = '22';
    $stdData['roll_num'] = '';
    $stdData['contact_no'] = null;
    $stdData['add_status'] = 1;
    $stdData['s_email'] = '';
    $stdData['ration_card_number'] = '';
    $stdData['ration_card_type'] = '';
    $stdData['caste_income_certificate_number'] = '';
    $stdData['extracurricular_activities'] ='';
    $stdData['quota'] = '';
    $stdData['student_sub_caste'] = '';
    $stdData['student_mobile_no'] = '';
    $stdData['blood_group'] = '';
    $stdData['donor_name'] = '';

    return $stdData;
  }

  private function _constrcut_father_array($suy_stddata, $parentType){
    $fatherData = array();
    $fatherData['first_name'] =($parentType == 'Mother')?'Father Of '.ucwords(strtolower($suy_stddata->student_first_name)):ucwords(strtolower($suy_stddata->parents_name));
    $fatherData['last_name'] = null;
    $fatherData['mobile_no'] = $suy_stddata->parent_mobile_number;
    $fatherData['qualification'] ='';
    $fatherData['occupation'] = '';
    $fatherData['annual_income'] = '';
    $fatherData['aadhar'] = '';
    $fatherData['email'] = ($parentType == 'Father')?$suy_stddata->email_id:'';
    $fatherData['userid'] = '';
    $fatherData['company'] = '';
    $fatherData['mother_tongue'] = '';
    $fatherData['designation'] = '';
    $fatherData['pan_number'] = '';
    $fatherData['userid'] = '';
    
    return $fatherData;
  }

  private function _constrcut_mother_array($suy_stddata, $parentType){
    $motherData = array();
    $motherData['first_name'] = ($parentType == 'Father')?'Mother Of '.ucwords(strtolower($suy_stddata->student_first_name)):ucwords(strtolower($suy_stddata->parents_name));
    $motherData['last_name'] = '';
    $motherData['mobile_no'] = $suy_stddata->parent_mobile_number;
    $motherData['qualification'] ='';
    $motherData['occupation'] = '';
    $motherData['annual_income'] = '';
    $motherData['aadhar'] = '';
    $motherData['email'] = ($parentType == 'Mother')?$suy_stddata->email_id:'';
    $motherData['userid'] = '';
    $motherData['company'] = '';
    $motherData['mother_tongue'] = '';
    $motherData['designation'] = '';
    $motherData['pan_number'] = '';
    $motherData['userid'] = '';
    
    return $motherData;
  }

  private function _generateAdmissionNo($config_admission, $params = []) {
      
    $admission_number = '';

    switch ($config_admission->admission_generation_algo) {
      case 'NPSAGA':
         $admission_number = $params['year_of_joining'].$config_admission->infix.$params['number'];
        break;
      case 'NH': {
          // Greater than 2 takes all the class from 1st to so on.
          if ($params['classid']->type >= 2) 
            $admission_number = $config_admission->infix.$params['number'];
          else 
            $admission_number = 'P'.$params['number'];            
        break;
      }
      case 'NEXTELEMENT': {
        $admission_number = $params['year_of_joining'].$config_admission->infix.$params['number'];
        break;
      }
      case 'YASHASVI': {
        $admission_number = $config_admission->infix.$params['number'];
        break;
      }
      case 'WPL':
      //Infix 'N' if nursery, 'P' if primary, 'H' if high school
        switch ($params['classid']->type) {
          case 1:
            $classType = 'N';
            break;
          case 2:
            $classType = 'P';
            break;
          case 3:
            $classType = 'H';
            break;
        }
        $admission_number =$config_admission->infix.$classType.$params['number'];
        break;
    }

    return $admission_number;

  }
  public function getAcadClassess()
  {
    $acad_year = $_POST['acad_year'];
    $getclassinfo = $this->Student_Model->getClassByAcadYear($acad_year);
    echo json_encode($getclassinfo);
  }
  public function getClassess()
  {
    if (isset($_POST['classid']) && isset($_POST['acad_year'])) {
      $classid = $_POST['classid'];
      $acad_year = $_POST['acad_year'];
      $getclassectioninfo = $this->Student_Model->getclassection($classid, $acad_year);
      echo json_encode($getclassectioninfo);
    } else if (isset($_POST['classid'])) {
      $classid = $_POST['classid'];
      $getclassectioninfo = $this->Student_Model->getclassection($classid);
      echo json_encode($getclassectioninfo);
    }
  }

  public function serach_student_fee_strucutre(){
    $friendly_name = $_POST['friendly_name'];
    $result = $this->fees_cohorts_model->get_fee_strucutre_filter_wise($friendly_name);
    echo json_encode($result);
  }


  public function assign_fee_strucutre_student_details(){
    $blueprintId = $_POST['blueprintId'];
    $cohort_id = $_POST['cohort_id'];
    $stdIds = $_POST['stdIds'];
    $suy_id = $_POST['suy_id'];
    // echo '<pre>';print_r($stdIds);die();
    $fee_amount = $this->_getCohort_feeAmount_student_details($blueprintId, $cohort_id);
    // echo '<pre>';print_r($fee_amount);die();
    $input = array();
    foreach ($fee_amount['insData'] as $key => $value) {
      foreach ($value as $key => $val) {
        $input['blueprint_installment_type_id'] = $val->feev2_blueprint_installment_types_id;
        $input['comp_amount'][$val->feev2_installment_id][$val->feev2_blueprint_component_id] = $val->compAmount;
        $input['concession_amount'][$val->feev2_installment_id][$val->feev2_blueprint_component_id] = 0;
        $input['concession_name'] = '';
      }

      foreach ($fee_amount['fine_amount'] as $insName => $fine_value) {
        foreach ($fine_value as $insId => $val) {
          $input['fine_amount'][$insId] = $val['fine_amount'];
        }
      }
    }
    $this->db->trans_begin();
    foreach ($stdIds as $key => $studentId) {      
      $rdata = $this->fees_student_model->insert_cohort_details($blueprintId, $cohort_id, 'STANDARD', $input['blueprint_installment_type_id'], $input['comp_amount'], $input['concession_amount'], $studentId, $input['concession_name'],$input['fine_amount']);
    }
    if (empty($rdata)){
      echo 0;
    }else{
      if ($this->db->trans_status()){
        $suy_update = $this->Suy_model->update_after_assing_fee_suy_enquiry($suy_id);
        $this->db->trans_commit();
        echo json_encode($rdata);
      }
    }
  }

  private function _getCohort_feeAmount_student_details($blueprintId, $cohort_id){   
    return $this->fees_collection_model->student_assign_fee_cohort_component_structure($cohort_id);
  }

  public function online_disabled_enabled_parent()
  {
    $std_cohort_id = $_POST['std_cohort_id'];
    echo  $this->fees_student_model->online_payment_disabled_parent_cohorts($std_cohort_id);
  }

  public function online_payment_enabled_parent()
  {
    $std_cohort_id = $_POST['std_cohort_id'];
    echo  $this->fees_student_model->online_payment_enabled_parent_cohorts($std_cohort_id);
  }

  public function Kolkata_datetime(){
    $timezone = new DateTimeZone("Asia/Kolkata" );
    $date = new DateTime();
    $date->setTimezone($timezone );
    $dtobj = $date->format('Y-m-d H:i:s');
    return $dtobj;
  }

  public function get_payment_link() {
    $suy_id = $_POST['suy_id'];
    $bpId = $_POST['bpId'];
    $amount = '10';
    $mobile_number = $_POST['phoneNum'];
    $input = array(
        'transaction_mode' => 'ONLINE',
        'receipt_date' => $this->Kolkata_datetime()
    );
  
    $student_fee_data = $this->Suy_model->get_student_fee_assinged_data($suy_id, $bpId);
    $inputMerge = array_merge($student_fee_data, $input);
    $fTransId = $this->Suy_model->insert_fee_transcation_suy($inputMerge, $amount);
    $link = $this->payment->get_payment_link($amount, $mobile_number, $suy_id, $fTransId);
    
    echo json_encode($link);
  }

  public function check_payment_link_by_suy_id(){
    $suy_id = $_POST['suy_id'];
    echo $this->Suy_model->check_payment_link_by_suy_id($suy_id);
  }

  public function regenerate_payment_link() {
    // $suy_id = $_POST['suy_id'];
    // $amount = '5000';
    // $mobile_number = $_POST['phoneNum'];
    // // echo '<pre>';print_r($mobile_number);die();
    // $link = $this->payment->get_payment_link($amount, $mobile_number, $suy_id);
    // echo json_encode($link);

    $suy_id = $_POST['suy_id'];
    $bpId = $_POST['bpId'];
    $amount = '10';
    $mobile_number = $_POST['phoneNum'];
    $input = array(
        'transaction_mode' => 'ONLINE',
        'receipt_date' => $this->Kolkata_datetime()
    );
  
    $student_fee_data = $this->Suy_model->get_student_fee_assinged_data($suy_id, $bpId);
    $inputMerge = array_merge($student_fee_data, $input);
    $fTransId = $this->Suy_model->insert_fee_transcation_suy($inputMerge, $amount);
    $link = $this->payment->get_payment_link($amount, $mobile_number, $suy_id, $fTransId);
    echo json_encode($link);

  }

  public function update_payment_link()
  {
    $suy_id = $_POST['suy_id'];
    $link = $_POST['link'];
    echo $this->Suy_model->updateSuy_paymentLink($suy_id ,$link);
  }

  public function get_student_parent_data(){
    $admission_id= $_POST['admission_id'];
    
    $result = $this->Suy_model->check_in_feesid_and_studentid($admission_id);
    $student_ids = (array)$result['student'];
    $parentsIds = $this->Suy_model->getStudentDetailsbyid($student_ids);
    // echo '<pre>';print_r($parentsIds);die();
    echo json_encode($parentsIds);

  }

  public function getPreview_email_template(){
    $pids = $_POST['pids'];
    $amount= $_POST['amount'];
    $link= $_POST['link'];
    $pData = array();
    foreach ($pids as $key => $val) {
        $explodeVal = explode('_', $val);
        $student = $this->Suy_model->getstudentData($explodeVal[0]);        
        $email_content = $this->Suy_model->get_email_template();
        $message_by = $student->email;
        $name = "$student->studentName";
        // $message_for_credit_calculation = '';
        $message = $email_content->content;
        $message = str_replace("%%amount%%",$amount , $message);
        $message = str_replace("%%payment_link%%",$link , $message);
        // echo '<pre>';print_r($message);die();
        // if(strlen($message_for_credit_calculation) < strlen($message)) {
        //     $message_for_credit_calculation = $message;
        //     //get the largest message for credits calculation
        // }
        // echo '<pre>';print_r($message_by);die();
        if($message_by != ''){
          $pData[] = array(
            'std_id' => $student->std_id,
            'pid' => $student->pid,
            'user_id' => $student->user_id,
            'relation_type' => $student->relation_type,
            'name' => $name,
            'message' => $message,
            'message_by' => $message_by,
            'send_type' =>$explodeVal[1]
        );

        }
        
    }
    echo json_encode(array('preview' => $pData));
  }

    public function send_payment_link_toParent(){
      $parentEmails = [];
      $messages = $_POST['messages'];
      $userIds = array();
      foreach ($messages as $ids => $msg) {
          list($stdId, $pId, $relation, $user_id, $sent_type) = explode("_",$ids);
          $parentEmails[$pId] = $msg;
          array_push($userIds, $user_id);
      }
      // echo '<pre>';print_r($parentEmails);die();
      $result = '0';
      $message = '';
      
      if (!empty($parentEmails)) {
          $emailStatus = $this->_communicateByEmail($parentEmails);
          if($emailStatus == 1) {
              $result = '1';
              $message = 'Successfully Email Sent';
              $this->session->set_flashdata('flashSuccess', 'Successfully Created');
              
          } else {
              $result = '0';
              $message = 'Something Went Wrong..';
              $this->session->set_flashdata('flashError', 'Something went wrong');
          }
      }
      
      echo json_encode(array('result'=>$result,'message'=>$message));
    }

    private function _communicateByEmail($parentEmails) {
      $parentIds = array();
      $userIds = array();
      foreach ($parentEmails as $pid => $pEmail) {
          $parentIds[] = $pid;
      }
    
      $emails = $this->user_provisioning_model->getEmailsByParentId($parentIds);
      $mailIds = array();
      $messages = array();
      $usernames = array();
      foreach ($emails as $key => $email) {
          array_push($mailIds, $email->email);
          array_push($usernames, $email->username);
          array_push($messages, $parentEmails[$email->parentId]);
      }
      $set = $this->Suy_model->get_email_template();  

      $data = array(
          'send_unique_emails' => 1,
          'message' => $messages,
          'subject' => (empty($set)) ? '' : $set->email_subject,
          'mail_username' => $usernames,
          'email_ids' => $mailIds,
          'template' => 'Parent Credentials'
      );
       return $this->_emailSender_provision($data);
    }

    public function _emailSender_provision($data){
      $set = $this->Suy_model->get_email_template();  
      $from_name = $this->settings->getSetting('school_name');     
      $from_email = (empty($set)) ? '' : $set->registered_email;
       // echo "<pre>"; print_r($from_email);die();
      $smtp_user = CONFIG_ENV['smtp_user'];
      $smtp_pass = urlencode(CONFIG_ENV['smtp_pass']);
      $smtp_host = CONFIG_ENV['smtp_host'];
      $smtp_port = CONFIG_ENV['smtp_port'];

      $data['from_email'] = $from_email;
      $data['from_name'] = $from_name;
      $data['smtp_user'] = $smtp_user;
      $data['smtp_pass'] = $smtp_pass;
      $data['smtp_host'] = $smtp_host;
      $data['smtp_port'] = $smtp_port;
      $data = http_build_query($data);
      $curl = curl_init();

      $username = CONFIG_ENV['job_server_username'];
      $password = CONFIG_ENV['job_server_password'];
      curl_setopt_array($curl, array(
          CURLOPT_URL => CONFIG_ENV['job_server_unique_email_uri'],
          CURLOPT_RETURNTRANSFER => true,
          CURLOPT_ENCODING => "",
          CURLOPT_MAXREDIRS => 10,
          CURLOPT_TIMEOUT => 30,
          CURLOPT_USERPWD => $username . ":" . $password,
          CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
          CURLOPT_CUSTOMREQUEST => "POST",
          CURLOPT_POST => 1,
          CURLOPT_POSTFIELDS => $data,
          CURLOPT_HTTPHEADER => array(
              "Accept: application/json",
              "Cache-Control: no-cache",
              "Content-Type: application/x-www-form-urlencoded",
              "Postman-Token: 090abdb9-b680-4492-b8b7-db81867b114e"
          ),
      ));

      $response = curl_exec($curl);

      $err = curl_error($curl);

      curl_close($curl);

      if ($err) {
        return 0;
      } else {
        return 1;
      }
  }

  private function _send_email_to_parent_and_facutlty_suy_eqnuiry($data, $from_name, $registered_email){

      $smtp_user = CONFIG_ENV['smtp_user'];
      $smtp_pass = urlencode(CONFIG_ENV['smtp_pass']);
      $smtp_host = CONFIG_ENV['smtp_host'];
      $smtp_port = CONFIG_ENV['smtp_port'];

      $data['from_email'] = $registered_email;
      $data['from_name'] = $from_name;
      $data['smtp_user'] = $smtp_user;
      $data['smtp_pass'] = $smtp_pass;
      $data['smtp_host'] = $smtp_host;
      $data['smtp_port'] = $smtp_port;
      $data = http_build_query($data);
      $curl = curl_init();

      $username = CONFIG_ENV['job_server_username'];
      $password = CONFIG_ENV['job_server_password'];
      curl_setopt_array($curl, array(
          CURLOPT_URL => CONFIG_ENV['job_server_unique_email_uri'],
          CURLOPT_RETURNTRANSFER => true,
          CURLOPT_ENCODING => "",
          CURLOPT_MAXREDIRS => 10,
          CURLOPT_TIMEOUT => 30,
          CURLOPT_USERPWD => $username . ":" . $password,
          CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
          CURLOPT_CUSTOMREQUEST => "POST",
          CURLOPT_POST => 1,
          CURLOPT_POSTFIELDS => $data,
          CURLOPT_HTTPHEADER => array(
              "Accept: application/json",
              "Cache-Control: no-cache",
              "Content-Type: application/x-www-form-urlencoded",
              "Postman-Token: 090abdb9-b680-4492-b8b7-db81867b114e"
          ),
      ));

      $response = curl_exec($curl);

      $err = curl_error($curl);

      curl_close($curl);

      if ($err) {
        return 0;
      } else {
        return 1;
      }
  }

  private function construct_email_suy_enquiry($message, $from_name, $suy_enquiry){
    $message = str_replace("%%student_name%%",$suy_enquiry->studentName , $message);
    $message = str_replace("%%class_name%%",$suy_enquiry->course_name , $message);
    $message = str_replace("%%school_name%%",$from_name , $message);
    $message = str_replace("%%amount%%",$suy_enquiry->amount_paid , $message);
    return $message;
  }

  public function send_email_move_to_admission(){

    $prof_email_id =$_POST['prof_email_id'];
    $suy_enquiryid =$_POST['suy_enquiryid'];
    $aproved_status = $this->Suy_model->update_suy_status($suy_enquiryid);
    $suy_enquiry =  $this->Suy_model->get_suy_enquiry_detial_email($suy_enquiryid);
    $from_name = $this->settings->getSetting('school_name');
    if (!empty($prof_email_id)) {
      $faculty_email_content = $this->Suy_model->get_faculty_email_template();
      $facultyMessage = $this->construct_email_suy_enquiry($faculty_email_content->content, $from_name,  $suy_enquiry);
      $data = array(
        'message' => [$facultyMessage],
        'subject' => (empty($faculty_email_content)) ? '' : $faculty_email_content->email_subject,
        'email_ids' =>[$prof_email_id],
        'template' => 'Faculty Email Suy Enquiry',
        'mail_username'=>''
      );

      $staff_email = $this->_send_email_to_parent_and_facutlty_suy_eqnuiry($data, $from_name, $faculty_email_content->registered_email);
      if ($staff_email) {
        echo 'faculty email sent';
      }else{
        echo '0';
      }
    }

    $parent_email_content = $this->Suy_model->get_parent_conformation_email_template();
    $parentMessage = $this->construct_email_suy_enquiry($parent_email_content->content, $from_name, $suy_enquiry);
    $parentdata = array(
      'message' => [$parentMessage],
      'subject' => (empty($parent_email_content)) ? '' : $parent_email_content->email_subject,
      'email_ids' =>[$suy_enquiry->email_id],
      'template' => 'Parent Email Suy Enquiry',
      'mail_username'=>''
    );
    $parentEmail = $this->_send_email_to_parent_and_facutlty_suy_eqnuiry($parentdata, $from_name, $parent_email_content->registered_email);
    if ($parentEmail) {
      echo 'Parent email sent';
    }else{
      echo '0';
    }
  }

    public function suy_detailsbyId() {
      $suy_id = $_POST['suy_enquiry_id'];
      $result = $this->Suy_model->get_suy_stddata_byId($suy_id);
      echo json_encode( $result);
    }

    public function get_batch_slot() {
      $std_adm_id = $_POST['std_adm_id'];
      $result = $this->Suy_model->get_batch_slot_byId($std_adm_id);
      echo json_encode( $result);
    }

    public function get_class_wise_student_names(){
      $class_id = $_POST['class_id'];
      $result = $this->Suy_model->get_class_wise_student_names($class_id);
      echo json_encode( $result);
    }

    public function get_course_names(){
      $class_id = $_POST['class_id'];
      // $class_section_id = $_POST['class_section_id'];
      $result = $this->Suy_model->get_course_names($class_id);
      echo json_encode( $result);
    }

    public function get_batch_names(){
      $new_cls_id = $_POST['new_cls_id'];
      $result = $this->Suy_model->get_batch_names($new_cls_id);
      echo json_encode( $result);
    }

    public function registerTo_New_batch(){
      $stdYear_data = $this->Suy_model->get_student_yearData($_POST['sa_id']);
      // echo '<pre>';print_r($stdYear_data);die();
      $result = $this->Suy_model->registerTo_New_batch($_POST,$stdYear_data);
      echo $result;
    }
  
}


?>