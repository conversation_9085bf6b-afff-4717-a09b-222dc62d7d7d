<ul class="breadcrumb">
    <li><a href="<?php echo site_url('dashboard');?>">Dashboard</a></li>
    <li><a href="<?php echo site_url('staff/Expense_controller/index');?>">Expense</a></li>
    <li>Add Expense Details</li>
</ul>
<hr>
<div class="col-md-12">
    <div class="panel panel-default new-panel-style_3">
        <div class="panel-heading new-panel-heading">
            <h3 class="panel-title"><strong>Add Expense</strong></h3>
            <ul class="panel-controls">
              <li class="back"><a href="<?php echo site_url('dashboard') ?>" class="control-primary">
                <span class="fa fa-mail-reply"></span></a> 
              </li>
            </ul>
            <a class="btn btn-info pull-right" href="<?php echo site_url('management/Expense/my_expenses') ?>" >My Expenses</a>
        </div>
            <div class="panel-body">
              <div class="col-md-12">
                  <form class="form-horizontal" enctype="multipart/form-data" action="<?php echo site_url('staff/Expense_controller/saveExpenseData'); ?>" method = 'POST'> 
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="col-md-2 col-xs-12 control-label">Name<font color="red">*</font></label>
                                <div class="col-md-8 col-xs-12">
                                    <input name="expense_name" type="text"  class="form-control input-md" data-parsley-error-message="Cannot be empty." required="">
                                </div>
                            </div>
                            <!-- <div class="form-group">
                                <label class="col-md-2 control-label" >Staff</label>
                                <div class="col-md-8">
                                  <select name="vendor_id" id="" class="form-control">
                                    <option value="">--Select Staff--</option>
                                  </select>
                                </div>
                            </div> -->
                            
                            <div class="form-group">
                                <label class="col-md-2 col-xs-12 control-label" for='event_date' id="d1" >Expense Date</label>
                                <div class="col-md-8 col-xs-12" id="d1a">
                                  <div class="input-group date" id="datePicker"> 
                                    <input type="text" class="form-control" id="date" name="date" placeholder="Select Date" required="" value="<?php echo date('d-m-Y'); ?>" >
                                    <span class="input-group-addon"><span class="glyphicon glyphicon-calendar"></span></span>
                                  </div>  
                                </div>
                            </div>
                                                        
                            <div class="form-group">
                                <label class="col-md-2 control-label">Voucher Upload</label>
                                <div class="col-md-8">
                                    <div class="input-group">
                                        <input type="file" id="voucher_file_input" class="form-control" accept=".pdf,.jpg,.jpeg,.png,.doc,.docx">
                                        <span class="input-group-btn">
                                            <button type="button" id="voucher_upload_btn" class="btn btn-primary">
                                                <i class="fa fa-upload"></i> Upload to S3
                                            </button>
                                        </span>
                                    </div>
                                    <input type="hidden" name="voucher_url" id="voucher_url">
                                    <span class="help-block">Allowed file types: PDF, JPG, PNG, DOC, DOCX (Max size: 5MB)</span>
                                    <div id="upload_status" style="display:none; margin-top: 10px;">
                                        <span id="upload_message"></span>
                                    </div>
                                    <p style="color:red;" id="fileuploadError"></p>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label class="col-md-2 control-label" >Vendor</label>
                                <div class="col-md-8">
                                  <select name="vendor_id" id="" class="form-control">
                                    <option value="">--Select Vendor--</option>
                                    <?php 
                                        foreach($vendor_list as $row){ ?>
                                          <option value="<?=$row->id ?>"><?= $row->vendor_name ?></option>
                                          
                                        <?php } ?>
                                  </select>
                                </div>
                            </div>


                        </div>

                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="col-md-2 control-label" >Amount<font color="red">*</font></label>
                                <div class="col-md-8">
                                  <input type="number" id="amount" name="amount" min="0" step="any" class="form-control">
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label class="col-md-2 control-label">Category<font color="red">*</font></label>
                                <div class="col-md-8">
                                  <select name="category_id" id="" class="form-control">
                                      <option value="">--Select Categry--</option>
                                      <?php 
                                        foreach($categories_list as $row){ ?>
                                          <option value="<?=$row->id ?>"><?= $row->category_name ?></option>
                                          
                                        <?php } ?>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label class="col-md-2 control-label" >Description</label>
                                <div class="col-md-8">
                                  <textarea name="description" type="text" class="form-control input-md" rows="4" placeholder="Enter Description"></textarea>
                                </div>
                            </div>
                            <br>
                        </div>
                    </div>
                  </form> 
                </div>  
              </div> 
            <div class='panel-footer new-footer'>
                <center>
                  <button type="submit" class="btn btn-success">Submit Expense and Continue</button>
                    <a class="btn btn-danger" href="<?php echo site_url('management/Expense'); ?>">Close</a>
                </center>
            </div>
           
    </div>
</div> 
<div class="visible-xs visible-sm">
  <a href="<?php echo site_url('staff/Expense_controller/index');?>" id="backBtn" onclick="loader()"><span class="fa fa-mail-reply"></span></a>
</div>



<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script type="text/javascript">
  $(document).ready(function() {
    var maxDate = new Date();
    maxDate.setDate(maxDate.getDate());
    var minDate = new Date();
    minDate.setFullYear( minDate.getFullYear() - 1);
    var dateNow = new Date();

    $('#datePicker,#select_date').datetimepicker({
      viewMode: 'days',
      format: 'DD-MM-YYYY'
    });
  });

  function Click() {
    var q = document.getElementById("name").value;
    if(q != 0) {
    swal("Loading !....", {
       button: false,
      });
     }
    }

  // Voucher Upload Functionality
  $('#voucher_upload_btn').on('click', async function () {
    const file = $('#voucher_file_input')[0].files[0];

    if (!file) {
        Swal.fire('No File Selected', 'Please select a file to upload.', 'warning');
        return;
    }

    // File size validation (5MB)
    const maxSize = 5 * 1024 * 1024; // 5MB in bytes
    if (file.size > maxSize) {
        Swal.fire('File Too Large', 'File size must be less than 5MB', 'error');
        return;
    }

    // File type validation
    const allowedTypes = ['application/pdf', 'image/jpeg', 'image/jpg', 'image/png',
                         'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
    if (!allowedTypes.includes(file.type)) {
        Swal.fire('Invalid File Type', 'Please upload PDF, JPG, PNG, DOC, or DOCX files only.', 'error');
        return;
    }

    Swal.fire({
        title: 'Preparing Upload...',
        text: 'Getting a secure link from server',
        allowOutsideClick: false,
        didOpen: () => {
            Swal.showLoading();
        }
    });

    try {
        const fileData = await getSignedUrl(file); // fileData = { path, fileName }
        if (!fileData) throw new Error('Upload failed');

        // Update UI and store file path
        $('#upload_status').show();
        $('#upload_message').html('<span style="color:green;"><i class="fa fa-check"></i> File uploaded successfully: ' + fileData.fileName + '</span>');
        $('#voucher_url').val(fileData.path);

        // Optional: Disable file input and show success
        $('#voucher_file_input').prop('disabled', true);
        $('#voucher_upload_btn').html('<i class="fa fa-check"></i> Uploaded').prop('disabled', true).removeClass('btn-primary').addClass('btn-success');

    } catch (err) {
        console.error(err);
        $('#upload_status').show();
        $('#upload_message').html('<span style="color:red;"><i class="fa fa-times"></i> Upload failed. Please try again.</span>');
    }
  });

  function getSignedUrl(file) {
    return new Promise((resolve, reject) => {
        // Step 1: Get signed URL
        $.ajax({
            url: '<?php echo site_url("S3_controller/getSignedUrl"); ?>',
            type: 'POST',
            data: {
                filename: file.name,
                file_type: file.type,
                folder: 'expense_vouchers'
            },
            success: function (response) {
                let data = JSON.parse(response)
                if (!data.signedUrl || !data.path) {
                    Swal.fire('Invalid Response', 'Server did not return a valid upload URL.', 'error');
                    reject('Invalid response');
                    return;
                }

                // Step 2: Upload file to S3
                Swal.fire({
                    title: 'Uploading...',
                    html: 'Uploading to S3: <b>0%</b>',
                    allowOutsideClick: false,
                    didOpen: () => {
                        Swal.showLoading();
                    },
                    willOpen: () => {
                        const content = Swal.getHtmlContainer().querySelector('b');

                        $.ajax({
                            url: data.signedUrl,
                            type: 'PUT',
                            headers: {
                                "Content-Type": file.type,
                                'x-amz-acl': 'public-read'
                            },
                            processData: false,
                            data: file,
                            xhr: function () {
                                const xhr = new XMLHttpRequest();
                                xhr.upload.addEventListener("progress", function (evt) {
                                    if (evt.lengthComputable) {
                                        const percentComplete = Math.round((evt.loaded / evt.total) * 100);
                                        content.textContent = `${percentComplete}%`;
                                    }
                                }, false);
                                return xhr;
                            },
                            success: function (_, status, xhr) {
                                if (xhr.status == 200 || xhr.status == 201) {
                                    Swal.fire({
                                        icon: 'success',
                                        title: 'Upload Complete!',
                                        text: 'Voucher uploaded successfully'
                                    });
                                    resolve({ path: data.path, fileName: file.name });
                                } else {
                                    Swal.fire('Unexpected Status', 'Upload finished with unknown status: ' + xhr.status, 'warning');
                                    reject('Unexpected status');
                                }
                            },
                            error: function (xhr, status, err) {
                                console.error('Upload Error:', err);
                                Swal.fire('Upload Failed', 'There was an error uploading the file.', 'error');
                                reject(err);
                            }
                        });
                    }
                });
            },
            error: function () {
                Swal.fire('Error', 'Failed to get signed URL from server.', 'error');
                reject('Request failed');
            }
        });
    });
  }

  // Reset upload if user selects a new file
  $('#voucher_file_input').on('change', function() {
    if (this.files.length > 0) {
        $('#voucher_upload_btn').html('<i class="fa fa-upload"></i> Upload to S3').prop('disabled', false).removeClass('btn-success').addClass('btn-primary');
        $('#voucher_file_input').prop('disabled', false);
        $('#upload_status').hide();
        $('#voucher_url').val('');
    }
  });
  </script>

  <style>
  @media (min-width: 768px){
.form-horizontal .control-label {
  text-align: left;
}
}
  </style>