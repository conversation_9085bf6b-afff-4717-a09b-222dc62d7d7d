
<ul class="breadcrumb">
    <li><a href="<?php echo site_url('dashboard');?>">Dashboard</a></li>
    <li><a href="<?php echo site_url('staff/Expense_controller/index');?>">Expense</a></li>
    <li>Add Expense Details</li>
</ul>
<hr>
<div class="col-md-12">
    <div class="panel panel-default new-panel-style_3">
        <div class="panel-heading new-panel-heading">
            <h3 class="panel-title"><strong>Add Expense</strong></h3>
            <ul class="panel-controls">
              <li class="back"><a href="<?php echo site_url('dashboard') ?>" class="control-primary">
                <span class="fa fa-mail-reply"></span></a> 
              </li>
            </ul>
            <a class="btn btn-info pull-right" href="<?php echo site_url('management/Expense/my_expenses') ?>" >My Expenses</a>
        </div>
            <div class="panel-body">
              <div class="col-md-12">
                  <form class="form-horizontal" enctype="multipart/form-data" action="<?php echo site_url('management/Expense/saveExpenseData'); ?>" method = 'POST'>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="col-md-2 col-xs-12 control-label">Name<font color="red">*</font></label>
                                <div class="col-md-8 col-xs-12">
                                    <input name="expense_name" type="text"  class="form-control input-md" data-parsley-error-message="Cannot be empty." required="">
                                </div>
                            </div>
                            <!-- <div class="form-group">
                                <label class="col-md-2 control-label" >Staff</label>
                                <div class="col-md-8">
                                  <select name="vendor_id" id="" class="form-control">
                                    <option value="">--Select Staff--</option>
                                  </select>
                                </div>
                            </div> -->
                            
                            <div class="form-group">
                                <label class="col-md-2 col-xs-12 control-label" for='event_date' id="d1" >Expense Date</label>
                                <div class="col-md-8 col-xs-12" id="d1a">
                                  <div class="input-group date" id="datePicker"> 
                                    <input type="text" class="form-control" id="date" name="date" placeholder="Select Date" required="" value="<?php echo date('d-m-Y'); ?>" >
                                    <span class="input-group-addon"><span class="glyphicon glyphicon-calendar"></span></span>
                                  </div>  
                                </div>
                            </div>
                                                        
                            <div class="form-group">
                                <label class="col-md-12">Voucher</label>
                                <div class="col-md-12">
                                    <input type="file" id="userfile" name="userfile" accept="application/pdf, image/png, image/jpeg" class="form-control">
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label class="col-md-2 control-label" >Vendor</label>
                                <div class="col-md-8">
                                  <select name="vendor_id" id="" class="form-control">
                                    <option value="">--Select Vendor--</option>
                                    <?php 
                                        foreach($vendor_list as $row){ ?>
                                          <option value="<?=$row->id ?>"><?= $row->vendor_name ?></option>
                                          
                                        <?php } ?>
                                  </select>
                                </div>
                            </div>


                        </div>

                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="col-md-2 control-label" >Amount<font color="red">*</font></label>
                                <div class="col-md-8">
                                  <input type="number" id="amount" name="amount" min="0" step="any" class="form-control">
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label class="col-md-2 control-label">Category<font color="red">*</font></label>
                                <div class="col-md-8">
                                  <select name="category_id" id="" class="form-control">
                                      <option value="">--Select Categry--</option>
                                      <?php 
                                        foreach($categories_list as $row){ ?>
                                          <option value="<?=$row->id ?>"><?= $row->category_name ?></option>
                                          
                                        <?php } ?>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label class="col-md-2 control-label" >Description</label>
                                <div class="col-md-8">
                                  <textarea name="description" type="text" class="form-control input-md" rows="4" placeholder="Enter Description"></textarea>
                                </div>
                            </div>
                            <br>
                        </div>
                    </div>

                    <div class='panel-footer new-footer'>
                        <center>
                          <button type="submit" class="btn btn-success">Submit Expense and Continue</button>
                            <a class="btn btn-danger" href="<?php echo site_url('management/Expense'); ?>">Close</a>
                        </center>
                    </div>
                  </form>
                </div>
              </div>
           
    </div>
</div> 
<div class="visible-xs visible-sm">
  <a href="<?php echo site_url('staff/Expense_controller/index');?>" id="backBtn" onclick="loader()"><span class="fa fa-mail-reply"></span></a>
</div>



<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script type="text/javascript">
  $(document).ready(function() {
    var maxDate = new Date();
    maxDate.setDate(maxDate.getDate());
    var minDate = new Date();
    minDate.setFullYear( minDate.getFullYear() - 1);
    var dateNow = new Date();

    $('#datePicker,#select_date').datetimepicker({
      viewMode: 'days',
      format: 'DD-MM-YYYY'
    });


  });

  function Click() {
    var q = document.getElementById("name").value;
    if(q != 0) {
    swal("Loading !....", {
       button: false,
      });
     }
    }

  // Voucher Upload Functionality - Auto upload when file is selected
  $('#userfile').on('change', async function () {
    alert("clicked");
    const file = $('#userfile')[0].files[0];

    if (!file) {
        Swal.fire('No File Selected', 'Please select a file to upload.', 'warning');
        return;
    }

    // File size validation (10MB)
    const maxSize = 10 * 1024 * 1024; // 10MB in bytes
    if (file.size > maxSize) {
        Swal.fire('File Too Large', 'File size must be less than 10MB', 'error');
        return;
    }

    // File type validation (only images)
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png'];
    if (!allowedTypes.includes(file.type)) {
        Swal.fire('Invalid File Type', 'Please upload JPG, JPEG, or PNG images only.', 'error');
        return;
    }

    Swal.fire({
        title: 'Preparing Upload...',
        text: 'Getting a secure link from server',
        allowOutsideClick: false,
        didOpen: () => {
            Swal.showLoading();
        }
    });

    try {
        const fileData = await getSignedUrl(file); // fileData = { path, fileName }
        if (!fileData) throw new Error('Upload failed');

        // Store the signed URL path directly in hidden input fields
        $('#voucher_url').val(fileData.path);
        $('#signed_url_path').val(fileData.path);
        console.log('File path saved:', fileData.path);
    } catch (err) {
        console.error(err);
    }
  });

  function getSignedUrl(file) {
    return new Promise((resolve, reject) => {
        // Step 1: Get signed URL
        $.ajax({
            url: '<?php echo site_url("S3_controller/getSignedUrl"); ?>',
            type: 'PUT',
            data: {
                filename: file.name,
                file_type: file.type,
                folder: 'expense_vouchers'
            },
            success: function (response) {
                let data = JSON.parse(response)
                if (!data.signedUrl || !data.path) {
                    Swal.fire('Invalid Response', 'Server did not return a valid upload URL.', 'error');
                    reject('Invalid response');
                    return;
                }

                // Step 2: Upload file to S3
                Swal.fire({
                    title: 'Uploading...',
                    html: 'Uploading to S3: <b>0%</b>',
                    allowOutsideClick: false,
                    didOpen: () => {
                        Swal.showLoading();
                    },
                    willOpen: () => {
                        const content = Swal.getHtmlContainer().querySelector('b');

                        $.ajax({
                            url: data.signedUrl,
                            type: 'PUT',
                            headers: {
                                "Content-Type": file.type,
                                'x-amz-acl': 'public-read'
                            },
                            processData: false,
                            data: file,
                            xhr: function () {
                                const xhr = new XMLHttpRequest();
                                xhr.upload.addEventListener("progress", function (evt) {
                                    if (evt.lengthComputable) {
                                        const percentComplete = Math.round((evt.loaded / evt.total) * 100);
                                        content.textContent = `${percentComplete}%`;
                                    }
                                }, false);
                                return xhr;
                            },
                            success: function (_, status, xhr) {
                                if (xhr.status == 200 || xhr.status == 201) {
                                    Swal.fire({
                                        icon: 'success',
                                        title: 'Upload Complete!',
                                        text: 'Voucher uploaded successfully'
                                    });
                                    resolve({ path: data.path, fileName: file.name });
                                } else {
                                    Swal.fire('Unexpected Status', 'Upload finished with unknown status: ' + xhr.status, 'warning');
                                    reject('Unexpected status');
                                }
                            },
                            error: function (xhr, status, err) {
                                console.error('Upload Error:', err);
                                Swal.fire('Upload Failed', 'There was an error uploading the file.', 'error');
                                reject(err);
                            }
                        });
                    }
                });
            },
            error: function () {
                Swal.fire('Error', 'Failed to get signed URL from server.', 'error');
                reject('Request failed');
            }
        });
    });
  }




  </script>

  <style>
  @media (min-width: 768px){
.form-horizontal .control-label {
  text-align: left;
}
}
  </style>
